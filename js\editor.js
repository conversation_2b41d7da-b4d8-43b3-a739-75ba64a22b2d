// Level editor for Cube Move game

class LevelEditor {
    constructor() {
        this.isActive = false;
        this.canvas = null;
        this.stage = null;
        this.container = new createjs.Container();
        
        // Editor state
        this.gridSize = 60;
        this.width = 10;
        this.height = 15;
        this.currentTool = 'path';
        this.isDrawing = false;
        
        // Level data
        this.levelData = {
            width: 10,
            height: 15,
            startPosition: { x: 5, y: 13 },
            endPosition: { x: 5, y: 1 },
            path: [],
            obstacles: [],
            specialTiles: [],
            backgroundColor: '#1a1a2e',
            pathColor: '#16213e',
            obstacleColor: '#0f3460',
            goalColor: '#e94560'
        };
        
        // Grid
        this.grid = [];
        this.gridVisual = null;
        
        // Tools
        this.tools = {
            path: { name: 'Path', color: '#16213e' },
            obstacle: { name: 'Obstacle', color: '#0f3460' },
            start: { name: 'Start', color: '#4ecdc4' },
            end: { name: 'Goal', color: '#e94560' },
            special: { name: 'Special', color: '#9c88ff' },
            erase: { name: 'Erase', color: '#ff6b6b' }
        };
        
        this.init();
    }

    init() {
        this.initializeGrid();
        this.createUI();
        this.setupEventListeners();
    }

    initializeGrid() {
        // Initialize empty grid
        this.grid = [];
        for (let y = 0; y < this.height; y++) {
            this.grid[y] = [];
            for (let x = 0; x < this.width; x++) {
                this.grid[y][x] = { type: 'empty', data: null };
            }
        }
        
        // Set default start and end positions
        this.grid[this.levelData.startPosition.y][this.levelData.startPosition.x] = {
            type: 'start',
            data: null
        };
        this.grid[this.levelData.endPosition.y][this.levelData.endPosition.x] = {
            type: 'end',
            data: null
        };
    }

    createUI() {
        // Create editor UI HTML
        const editorHTML = `
            <div id="levelEditor" class="level-editor hidden">
                <div class="editor-container">
                    <div class="editor-header">
                        <h2>Level Editor</h2>
                        <div class="editor-controls">
                            <button id="editorSave" class="editor-btn">Save</button>
                            <button id="editorTest" class="editor-btn">Test</button>
                            <button id="editorClear" class="editor-btn">Clear</button>
                            <button id="editorClose" class="editor-btn">Close</button>
                        </div>
                    </div>
                    
                    <div class="editor-content">
                        <div class="editor-tools">
                            <h3>Tools</h3>
                            <div class="tool-buttons">
                                ${Object.entries(this.tools).map(([key, tool]) => 
                                    `<button class="tool-btn ${key === 'path' ? 'active' : ''}" 
                                             data-tool="${key}" 
                                             style="background-color: ${tool.color}">
                                        ${tool.name}
                                    </button>`
                                ).join('')}
                            </div>
                            
                            <div class="editor-settings">
                                <h3>Settings</h3>
                                <label>
                                    Grid Size:
                                    <input type="range" id="gridSizeSlider" min="40" max="80" value="60">
                                    <span id="gridSizeValue">60</span>
                                </label>
                                
                                <label>
                                    Background Color:
                                    <input type="color" id="bgColorPicker" value="#1a1a2e">
                                </label>
                                
                                <label>
                                    Path Color:
                                    <input type="color" id="pathColorPicker" value="#16213e">
                                </label>
                            </div>
                        </div>
                        
                        <div class="editor-canvas-container">
                            <canvas id="editorCanvas" width="600" height="900"></canvas>
                            <div class="editor-instructions">
                                <p>Click and drag to place tiles</p>
                                <p>Use tools on the left to change tile types</p>
                                <p>Start (cyan) and Goal (red) can only have one each</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Add to document
        document.body.insertAdjacentHTML('beforeend', editorHTML);
    }

    setupEventListeners() {
        // Tool buttons
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.selectTool(btn.dataset.tool);
                
                // Update active button
                document.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });
        
        // Control buttons
        document.getElementById('editorSave')?.addEventListener('click', () => this.saveLevel());
        document.getElementById('editorTest')?.addEventListener('click', () => this.testLevel());
        document.getElementById('editorClear')?.addEventListener('click', () => this.clearLevel());
        document.getElementById('editorClose')?.addEventListener('click', () => this.close());
        
        // Settings
        const gridSizeSlider = document.getElementById('gridSizeSlider');
        const gridSizeValue = document.getElementById('gridSizeValue');
        
        gridSizeSlider?.addEventListener('input', () => {
            this.gridSize = parseInt(gridSizeSlider.value);
            gridSizeValue.textContent = this.gridSize;
            this.redrawGrid();
        });
        
        document.getElementById('bgColorPicker')?.addEventListener('change', (e) => {
            this.levelData.backgroundColor = e.target.value;
            this.redrawGrid();
        });
        
        document.getElementById('pathColorPicker')?.addEventListener('change', (e) => {
            this.levelData.pathColor = e.target.value;
            this.redrawGrid();
        });
    }

    open() {
        this.isActive = true;
        
        // Show editor UI
        const editor = document.getElementById('levelEditor');
        if (editor) {
            editor.classList.remove('hidden');
        }
        
        // Initialize canvas
        this.canvas = document.getElementById('editorCanvas');
        if (this.canvas) {
            this.stage = new createjs.Stage(this.canvas);
            this.stage.addChild(this.container);
            this.stage.enableMouseOver(10);
            
            // Setup canvas events
            this.setupCanvasEvents();
            
            // Draw initial grid
            this.redrawGrid();
            
            // Start update loop
            createjs.Ticker.addEventListener('tick', this.stage);
        }
    }

    close() {
        this.isActive = false;
        
        // Hide editor UI
        const editor = document.getElementById('levelEditor');
        if (editor) {
            editor.classList.add('hidden');
        }
        
        // Cleanup canvas
        if (this.stage) {
            createjs.Ticker.removeEventListener('tick', this.stage);
            this.stage = null;
        }
        this.canvas = null;
    }

    setupCanvasEvents() {
        if (!this.canvas) return;
        
        this.canvas.addEventListener('mousedown', (e) => {
            this.isDrawing = true;
            this.handleCanvasClick(e);
        });
        
        this.canvas.addEventListener('mousemove', (e) => {
            if (this.isDrawing) {
                this.handleCanvasClick(e);
            }
        });
        
        this.canvas.addEventListener('mouseup', () => {
            this.isDrawing = false;
        });
        
        this.canvas.addEventListener('mouseleave', () => {
            this.isDrawing = false;
        });
    }

    handleCanvasClick(event) {
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        // Convert to grid coordinates
        const gridX = Math.floor(x / this.gridSize);
        const gridY = Math.floor(y / this.gridSize);
        
        if (gridX >= 0 && gridX < this.width && gridY >= 0 && gridY < this.height) {
            this.placeTile(gridX, gridY);
        }
    }

    placeTile(gridX, gridY) {
        const currentTile = this.grid[gridY][gridX];
        
        switch (this.currentTool) {
            case 'path':
                this.grid[gridY][gridX] = { type: 'path', data: null };
                break;
                
            case 'obstacle':
                this.grid[gridY][gridX] = { type: 'obstacle', data: { obstacleType: 'wall' } };
                break;
                
            case 'start':
                // Remove previous start
                this.removeAllOfType('start');
                this.grid[gridY][gridX] = { type: 'start', data: null };
                this.levelData.startPosition = { x: gridX, y: gridY };
                break;
                
            case 'end':
                // Remove previous end
                this.removeAllOfType('end');
                this.grid[gridY][gridX] = { type: 'end', data: null };
                this.levelData.endPosition = { x: gridX, y: gridY };
                break;
                
            case 'special':
                this.grid[gridY][gridX] = { type: 'special', data: { specialType: 'teleporter' } };
                break;
                
            case 'erase':
                this.grid[gridY][gridX] = { type: 'empty', data: null };
                break;
        }
        
        this.redrawGrid();
    }

    removeAllOfType(type) {
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                if (this.grid[y][x].type === type) {
                    this.grid[y][x] = { type: 'empty', data: null };
                }
            }
        }
    }

    selectTool(tool) {
        this.currentTool = tool;
    }

    redrawGrid() {
        if (!this.stage) return;
        
        this.container.removeAllChildren();
        
        // Draw background
        const bg = new createjs.Shape();
        bg.graphics
            .beginFill(this.levelData.backgroundColor)
            .drawRect(0, 0, this.width * this.gridSize, this.height * this.gridSize);
        this.container.addChild(bg);
        
        // Draw grid lines
        const gridLines = new createjs.Shape();
        gridLines.graphics.setStrokeStyle(1).beginStroke('rgba(255, 255, 255, 0.1)');
        
        for (let x = 0; x <= this.width; x++) {
            gridLines.graphics.moveTo(x * this.gridSize, 0);
            gridLines.graphics.lineTo(x * this.gridSize, this.height * this.gridSize);
        }
        
        for (let y = 0; y <= this.height; y++) {
            gridLines.graphics.moveTo(0, y * this.gridSize);
            gridLines.graphics.lineTo(this.width * this.gridSize, y * this.gridSize);
        }
        
        this.container.addChild(gridLines);
        
        // Draw tiles
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                const tile = this.grid[y][x];
                if (tile.type !== 'empty') {
                    const tileShape = this.createEditorTile(x, y, tile);
                    this.container.addChild(tileShape);
                }
            }
        }
        
        this.stage.update();
    }

    createEditorTile(gridX, gridY, tile) {
        const shape = new createjs.Shape();
        const x = gridX * this.gridSize;
        const y = gridY * this.gridSize;
        const size = this.gridSize;
        
        let color = '#ffffff';
        
        switch (tile.type) {
            case 'path':
                color = this.levelData.pathColor;
                break;
            case 'obstacle':
                color = this.levelData.obstacleColor;
                break;
            case 'start':
                color = '#4ecdc4';
                break;
            case 'end':
                color = this.levelData.goalColor;
                break;
            case 'special':
                color = '#9c88ff';
                break;
        }
        
        shape.graphics
            .beginFill(color)
            .drawRoundRect(x + 2, y + 2, size - 4, size - 4, 8);
        
        // Add icon for special tiles
        if (tile.type === 'start') {
            shape.graphics
                .beginFill('#ffffff')
                .drawCircle(x + size/2, y + size/2, size * 0.2);
        } else if (tile.type === 'end') {
            shape.graphics
                .beginFill('#ffffff')
                .drawCircle(x + size/2, y + size/2, size * 0.15);
        }
        
        return shape;
    }

    saveLevel() {
        // Convert grid to level data format
        this.levelData.path = [];
        this.levelData.obstacles = [];
        this.levelData.specialTiles = [];
        
        for (let y = 0; y < this.height; y++) {
            for (let x = 0; x < this.width; x++) {
                const tile = this.grid[y][x];
                
                switch (tile.type) {
                    case 'path':
                        this.levelData.path.push({ x, y });
                        break;
                    case 'obstacle':
                        this.levelData.obstacles.push({ 
                            x, y, 
                            type: tile.data?.obstacleType || 'wall' 
                        });
                        break;
                    case 'special':
                        this.levelData.specialTiles.push({ 
                            x, y, 
                            type: tile.data?.specialType || 'teleporter' 
                        });
                        break;
                }
            }
        }
        
        // Save to localStorage
        const customLevels = Utils.loadFromStorage('cubeMove_customLevels', []);
        const levelId = 'custom_' + Date.now();
        
        customLevels.push({
            id: levelId,
            name: `Custom Level ${customLevels.length + 1}`,
            data: Utils.deepClone(this.levelData),
            created: new Date().toISOString()
        });
        
        Utils.saveToStorage('cubeMove_customLevels', customLevels);
        
        alert('Level saved successfully!');
    }

    testLevel() {
        // Convert current grid to level data and test it
        this.saveLevel();
        
        // TODO: Implement level testing
        alert('Level testing not yet implemented');
    }

    clearLevel() {
        if (confirm('Are you sure you want to clear the level?')) {
            this.initializeGrid();
            this.redrawGrid();
        }
    }

    loadLevel(levelData) {
        // Load existing level data into editor
        this.levelData = Utils.deepClone(levelData);
        this.initializeGrid();
        
        // Populate grid from level data
        if (levelData.path) {
            levelData.path.forEach(pos => {
                this.grid[pos.y][pos.x] = { type: 'path', data: null };
            });
        }
        
        if (levelData.obstacles) {
            levelData.obstacles.forEach(obs => {
                this.grid[obs.y][obs.x] = { 
                    type: 'obstacle', 
                    data: { obstacleType: obs.type } 
                };
            });
        }
        
        if (levelData.specialTiles) {
            levelData.specialTiles.forEach(tile => {
                this.grid[tile.y][tile.x] = { 
                    type: 'special', 
                    data: { specialType: tile.type } 
                };
            });
        }
        
        this.redrawGrid();
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LevelEditor;
}
