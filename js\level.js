// Level system for Cube Move game

class Level {
    constructor(levelData) {
        this.data = levelData;
        this.container = new createjs.Container();
        this.gridSize = 60;
        this.tiles = [];
        this.obstacles = [];
        this.specialTiles = [];
        
        // Level properties
        this.width = levelData.width || 10;
        this.height = levelData.height || 15;
        this.startPosition = levelData.startPosition || { x: 5, y: 13 };
        this.endPosition = levelData.endPosition || { x: 5, y: 1 };
        
        // Visual properties
        this.backgroundColor = levelData.backgroundColor || '#1a1a2e';
        this.pathColor = levelData.pathColor || '#16213e';
        this.obstacleColor = levelData.obstacleColor || '#0f3460';
        this.goalColor = levelData.goalColor || '#e94560';
        
        this.init();
    }

    init() {
        this.createBackground();
        this.createPath();
        this.createObstacles();
        this.createGoal();
        this.createSpecialTiles();
        
        // Center the level on screen
        this.centerLevel();
    }

    createBackground() {
        const bg = new createjs.Shape();
        bg.graphics
            .beginFill(this.backgroundColor)
            .drawRect(0, 0, 768, 1024);
        this.container.addChild(bg);
    }

    createPath() {
        const path = this.data.path || this.generateDefaultPath();
        
        path.forEach(tile => {
            const tileShape = this.createTile(tile.x, tile.y, this.pathColor);
            this.tiles.push({
                x: tile.x,
                y: tile.y,
                type: 'path',
                shape: tileShape
            });
            this.container.addChild(tileShape);
        });
    }

    createTile(gridX, gridY, color, size = this.gridSize) {
        const shape = new createjs.Shape();
        const x = gridX * this.gridSize;
        const y = gridY * this.gridSize;
        
        // Main tile
        shape.graphics
            .beginFill(color)
            .drawRoundRect(x + 2, y + 2, size - 4, size - 4, 8);
        
        // Highlight
        const highlightColor = this.lightenColor(color, 0.2);
        shape.graphics
            .beginFill(highlightColor)
            .drawRoundRect(x + 2, y + 2, size - 4, (size - 4) * 0.3, 8);
        
        // Border
        shape.graphics
            .setStrokeStyle(1)
            .beginStroke(this.darkenColor(color, 0.3))
            .drawRoundRect(x + 2, y + 2, size - 4, size - 4, 8);
        
        return shape;
    }

    createObstacles() {
        const obstacles = this.data.obstacles || [];
        
        obstacles.forEach(obstacle => {
            const obstacleShape = this.createObstacle(obstacle.x, obstacle.y, obstacle.type);
            this.obstacles.push({
                x: obstacle.x,
                y: obstacle.y,
                type: obstacle.type || 'wall',
                shape: obstacleShape
            });
            this.container.addChild(obstacleShape);
        });
    }

    createObstacle(gridX, gridY, type = 'wall') {
        const shape = new createjs.Shape();
        const x = gridX * this.gridSize;
        const y = gridY * this.gridSize;
        const size = this.gridSize;
        
        switch (type) {
            case 'wall':
                // Solid wall
                shape.graphics
                    .beginFill(this.obstacleColor)
                    .drawRoundRect(x + 1, y + 1, size - 2, size - 2, 4);
                
                // Top highlight
                shape.graphics
                    .beginFill(this.lightenColor(this.obstacleColor, 0.3))
                    .drawRoundRect(x + 1, y + 1, size - 2, (size - 2) * 0.2, 4);
                break;
                
            case 'spike':
                // Dangerous spikes
                shape.graphics
                    .beginFill('#ff4757')
                    .drawPolyStar(x + size/2, y + size/2, size * 0.3, 8, 0.5, 0);
                break;
                
            case 'hole':
                // Pit/hole
                shape.graphics
                    .beginFill('#000000')
                    .drawCircle(x + size/2, y + size/2, size * 0.4);
                
                // Inner shadow effect
                shape.graphics
                    .beginRadialGradientFill(
                        ['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.3)'],
                        [0, 1],
                        x + size/2, y + size/2, 0,
                        x + size/2, y + size/2, size * 0.4
                    )
                    .drawCircle(x + size/2, y + size/2, size * 0.4);
                break;
        }
        
        return shape;
    }

    createGoal() {
        const goalShape = this.createGoalTile(this.endPosition.x, this.endPosition.y);
        this.container.addChild(goalShape);
        
        this.goalTile = {
            x: this.endPosition.x,
            y: this.endPosition.y,
            type: 'goal',
            shape: goalShape
        };
    }

    createGoalTile(gridX, gridY) {
        const container = new createjs.Container();
        const x = gridX * this.gridSize;
        const y = gridY * this.gridSize;
        const size = this.gridSize;
        
        // Base tile
        const base = new createjs.Shape();
        base.graphics
            .beginFill(this.pathColor)
            .drawRoundRect(x + 2, y + 2, size - 4, size - 4, 8);
        container.addChild(base);
        
        // Goal hole
        const hole = new createjs.Shape();
        hole.graphics
            .beginFill(this.goalColor)
            .drawCircle(x + size/2, y + size/2, size * 0.25);
        
        // Glow effect
        hole.graphics
            .beginRadialGradientFill(
                [this.goalColor, 'rgba(233, 69, 96, 0.3)'],
                [0, 1],
                x + size/2, y + size/2, 0,
                x + size/2, y + size/2, size * 0.4
            )
            .drawCircle(x + size/2, y + size/2, size * 0.4);
        
        container.addChild(hole);
        
        // Animate the goal
        createjs.Tween.get(hole, { loop: true })
            .to({ scaleX: 1.1, scaleY: 1.1 }, 1000, createjs.Ease.quadInOut)
            .to({ scaleX: 1.0, scaleY: 1.0 }, 1000, createjs.Ease.quadInOut);
        
        return container;
    }

    createSpecialTiles() {
        const specialTiles = this.data.specialTiles || [];
        
        specialTiles.forEach(tile => {
            const tileShape = this.createSpecialTile(tile.x, tile.y, tile.type);
            this.specialTiles.push({
                x: tile.x,
                y: tile.y,
                type: tile.type,
                shape: tileShape,
                properties: tile.properties || {}
            });
            this.container.addChild(tileShape);
        });
    }

    createSpecialTile(gridX, gridY, type) {
        const shape = new createjs.Shape();
        const x = gridX * this.gridSize;
        const y = gridY * this.gridSize;
        const size = this.gridSize;
        
        // Base tile
        shape.graphics
            .beginFill(this.pathColor)
            .drawRoundRect(x + 2, y + 2, size - 4, size - 4, 8);
        
        // Special tile overlay
        switch (type) {
            case 'teleporter':
                shape.graphics
                    .beginFill('#9c88ff')
                    .drawCircle(x + size/2, y + size/2, size * 0.3);
                break;
                
            case 'switch':
                shape.graphics
                    .beginFill('#ffa502')
                    .drawRect(x + size * 0.3, y + size * 0.3, size * 0.4, size * 0.4);
                break;
                
            case 'ice':
                shape.graphics
                    .beginFill('#70a1ff')
                    .drawRoundRect(x + 2, y + 2, size - 4, size - 4, 8);
                break;
        }
        
        return shape;
    }

    generateDefaultPath() {
        // Generate a simple path from start to end
        const path = [];
        const startX = this.startPosition.x;
        const startY = this.startPosition.y;
        const endX = this.endPosition.x;
        const endY = this.endPosition.y;
        
        // Create straight path
        for (let y = Math.min(startY, endY); y <= Math.max(startY, endY); y++) {
            path.push({ x: startX, y: y });
        }
        
        // Add horizontal connection if needed
        if (startX !== endX) {
            const connectY = endY;
            for (let x = Math.min(startX, endX); x <= Math.max(startX, endX); x++) {
                if (!path.find(p => p.x === x && p.y === connectY)) {
                    path.push({ x: x, y: connectY });
                }
            }
        }
        
        return path;
    }

    centerLevel() {
        // Center the level container on the canvas
        const canvasWidth = 768;
        const canvasHeight = 1024;
        const levelWidth = this.width * this.gridSize;
        const levelHeight = this.height * this.gridSize;
        
        this.container.x = (canvasWidth - levelWidth) / 2;
        this.container.y = (canvasHeight - levelHeight) / 2;
    }

    // Game logic methods
    isValidPosition(worldX, worldY) {
        const gridPos = this.worldToGrid(worldX, worldY);
        
        // Check bounds
        if (gridPos.x < 0 || gridPos.x >= this.width || 
            gridPos.y < 0 || gridPos.y >= this.height) {
            return false;
        }
        
        // Check if there's a path tile at this position
        return this.tiles.some(tile => tile.x === gridPos.x && tile.y === gridPos.y);
    }

    hasObstacle(worldX, worldY) {
        const gridPos = this.worldToGrid(worldX, worldY);
        return this.obstacles.some(obstacle => 
            obstacle.x === gridPos.x && obstacle.y === gridPos.y
        );
    }

    isWinPosition(worldX, worldY) {
        const gridPos = this.worldToGrid(worldX, worldY);
        return gridPos.x === this.endPosition.x && gridPos.y === this.endPosition.y;
    }

    isLosePosition(worldX, worldY) {
        const gridPos = this.worldToGrid(worldX, worldY);
        const obstacle = this.obstacles.find(obs => 
            obs.x === gridPos.x && obs.y === gridPos.y
        );
        return obstacle && (obstacle.type === 'spike' || obstacle.type === 'hole');
    }

    getStartPosition() {
        return this.gridToWorld(this.startPosition.x, this.startPosition.y);
    }

    getGridSize() {
        return this.gridSize;
    }

    worldToGrid(worldX, worldY) {
        // Convert world coordinates to grid coordinates
        const localX = worldX - this.container.x;
        const localY = worldY - this.container.y;
        
        return {
            x: Math.round(localX / this.gridSize),
            y: Math.round(localY / this.gridSize)
        };
    }

    gridToWorld(gridX, gridY) {
        // Convert grid coordinates to world coordinates
        return {
            x: this.container.x + (gridX * this.gridSize) + (this.gridSize / 2),
            y: this.container.y + (gridY * this.gridSize) + (this.gridSize / 2)
        };
    }

    onCubeMove(position, direction) {
        const gridPos = this.worldToGrid(position.x, position.y);
        
        // Check for special tiles
        const specialTile = this.specialTiles.find(tile => 
            tile.x === gridPos.x && tile.y === gridPos.y
        );
        
        if (specialTile) {
            this.handleSpecialTile(specialTile, direction);
        }
    }

    handleSpecialTile(tile, direction) {
        switch (tile.type) {
            case 'teleporter':
                // TODO: Implement teleporter logic
                console.log('Teleporter activated');
                break;
                
            case 'switch':
                // TODO: Implement switch logic
                console.log('Switch activated');
                break;
                
            case 'ice':
                // TODO: Implement ice sliding logic
                console.log('Ice tile - sliding');
                break;
        }
    }

    update(deltaTime) {
        // Update any animated elements
        // This is called every frame during gameplay
    }

    // Color utility methods
    lightenColor(color, amount) {
        const rgb = Utils.hexToRgb(color);
        if (!rgb) return color;
        
        const r = Math.min(255, Math.floor(rgb.r + (255 - rgb.r) * amount));
        const g = Math.min(255, Math.floor(rgb.g + (255 - rgb.g) * amount));
        const b = Math.min(255, Math.floor(rgb.b + (255 - rgb.b) * amount));
        
        return Utils.rgbToHex(r, g, b);
    }

    darkenColor(color, amount) {
        const rgb = Utils.hexToRgb(color);
        if (!rgb) return color;
        
        const r = Math.floor(rgb.r * (1 - amount));
        const g = Math.floor(rgb.g * (1 - amount));
        const b = Math.floor(rgb.b * (1 - amount));
        
        return Utils.rgbToHex(r, g, b);
    }

    // Cleanup
    destroy() {
        createjs.Tween.removeAllTweens();
        
        if (this.container.parent) {
            this.container.parent.removeChild(this.container);
        }
        
        this.tiles = [];
        this.obstacles = [];
        this.specialTiles = [];
    }
}

// Level Manager class
class LevelManager {
    constructor() {
        this.levels = {};
        this.totalLevels = 30;
    }

    async init() {
        // Generate all 30 levels
        for (let i = 1; i <= this.totalLevels; i++) {
            this.levels[i] = this.generateLevel(i);
        }
        
        console.log(`Generated ${this.totalLevels} levels`);
    }

    generateLevel(levelNumber) {
        // Generate procedural levels with increasing difficulty
        const difficulty = Math.floor((levelNumber - 1) / 5) + 1; // 1-6 difficulty levels
        
        const levelData = {
            id: levelNumber,
            width: 10,
            height: 15,
            startPosition: { x: 5, y: 13 },
            endPosition: { x: 5, y: 1 },
            backgroundColor: '#1a1a2e',
            pathColor: '#16213e',
            obstacleColor: '#0f3460',
            goalColor: '#e94560'
        };
        
        // Generate path based on difficulty
        levelData.path = this.generateLevelPath(levelNumber, difficulty);
        
        // Add obstacles based on difficulty
        levelData.obstacles = this.generateObstacles(levelNumber, difficulty, levelData.path);
        
        // Add special tiles for higher levels
        if (levelNumber > 10) {
            levelData.specialTiles = this.generateSpecialTiles(levelNumber, difficulty, levelData.path);
        }
        
        return levelData;
    }

    generateLevelPath(levelNumber, difficulty) {
        // Create more complex paths for higher difficulties
        const path = [];
        const startX = 5, startY = 13;
        const endX = 5, endY = 1;
        
        if (difficulty <= 2) {
            // Simple straight or L-shaped paths
            return this.generateSimplePath(startX, startY, endX, endY);
        } else {
            // More complex zigzag paths
            return this.generateComplexPath(startX, startY, endX, endY, difficulty);
        }
    }

    generateSimplePath(startX, startY, endX, endY) {
        const path = [];
        
        // Straight vertical path
        for (let y = startY; y >= endY; y--) {
            path.push({ x: startX, y: y });
        }
        
        return path;
    }

    generateComplexPath(startX, startY, endX, endY, difficulty) {
        const path = [];
        let currentX = startX;
        let currentY = startY;
        
        path.push({ x: currentX, y: currentY });
        
        while (currentY > endY) {
            // Randomly choose direction
            const directions = ['up'];
            if (currentX > 1) directions.push('left');
            if (currentX < 8) directions.push('right');
            
            const direction = Utils.randomChoice(directions);
            const steps = Utils.randomInt(1, Math.min(3, difficulty));
            
            for (let i = 0; i < steps && currentY > endY; i++) {
                switch (direction) {
                    case 'up':
                        currentY--;
                        break;
                    case 'left':
                        currentX = Math.max(1, currentX - 1);
                        break;
                    case 'right':
                        currentX = Math.min(8, currentX + 1);
                        break;
                }
                
                if (currentY >= endY) {
                    path.push({ x: currentX, y: currentY });
                }
            }
        }
        
        // Ensure we reach the end
        if (currentX !== endX) {
            while (currentX !== endX) {
                currentX += currentX < endX ? 1 : -1;
                path.push({ x: currentX, y: currentY });
            }
        }
        
        return path;
    }

    generateObstacles(levelNumber, difficulty, path) {
        const obstacles = [];
        const numObstacles = Math.min(difficulty * 2, 10);
        
        for (let i = 0; i < numObstacles; i++) {
            let attempts = 0;
            while (attempts < 20) {
                const x = Utils.randomInt(1, 8);
                const y = Utils.randomInt(2, 12);
                
                // Don't place obstacles on the path
                const onPath = path.some(tile => tile.x === x && tile.y === y);
                const tooClose = obstacles.some(obs => 
                    Math.abs(obs.x - x) <= 1 && Math.abs(obs.y - y) <= 1
                );
                
                if (!onPath && !tooClose) {
                    const type = difficulty > 3 ? Utils.randomChoice(['wall', 'spike', 'hole']) : 'wall';
                    obstacles.push({ x, y, type });
                    break;
                }
                attempts++;
            }
        }
        
        return obstacles;
    }

    generateSpecialTiles(levelNumber, difficulty, path) {
        const specialTiles = [];
        
        if (difficulty > 4) {
            // Add some special tiles for advanced levels
            const numSpecial = Utils.randomInt(1, 3);
            
            for (let i = 0; i < numSpecial; i++) {
                const pathTile = Utils.randomChoice(path.slice(1, -1)); // Exclude start and end
                const type = Utils.randomChoice(['teleporter', 'switch', 'ice']);
                
                specialTiles.push({
                    x: pathTile.x,
                    y: pathTile.y,
                    type: type,
                    properties: {}
                });
            }
        }
        
        return specialTiles;
    }

    getLevel(levelNumber) {
        return this.levels[levelNumber] || null;
    }

    hasLevel(levelNumber) {
        return !!this.levels[levelNumber];
    }

    getTotalLevels() {
        return this.totalLevels;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Level, LevelManager };
}
