// Main initialization file for Cube Move game

class GameApp {
    constructor() {
        this.canvas = null;
        this.stage = null;
        this.game = null;
        this.ui = null;
        this.audio = null;
        this.input = null;
        this.isInitialized = false;
        this.loadingProgress = 0;
        
        // Game settings
        this.settings = {
            sound: true,
            music: true,
            cubeColor: '#FFD700',
            cubeSize: 1.0,
            swipeSensitivity: 1.0
        };
        
        // Game data
        this.gameData = {
            currentLevel: 1,
            unlockedLevels: 1,
            completedLevels: [],
            bestTimes: {},
            totalScore: 0
        };
    }

    async init() {
        try {
            // Check browser support
            if (!Utils.checkBrowserSupport()) {
                Utils.showBrowserNotSupported();
                return;
            }

            // Show loading screen
            Utils.showLoading(true);
            this.updateProgress(10);

            // Load settings and game data
            this.loadSettings();
            this.loadGameData();
            this.updateProgress(20);

            // Initialize canvas and CreateJS stage
            this.initCanvas();
            this.updateProgress(30);

            // Initialize game systems
            await this.initSystems();
            this.updateProgress(60);

            // Setup event listeners
            this.setupEventListeners();
            this.updateProgress(80);

            // Initialize UI
            this.initUI();
            this.updateProgress(90);

            // Handle device rotation
            Utils.handleDeviceRotation();
            this.updateProgress(100);

            // Hide loading screen and show main menu
            setTimeout(() => {
                Utils.showLoading(false);
                this.showMainMenu();
                this.isInitialized = true;
            }, 500);

        } catch (error) {
            console.error('Failed to initialize game:', error);
            this.showError('Failed to load game. Please refresh the page.');
        }
    }

    initCanvas() {
        this.canvas = document.getElementById('gameCanvas');
        if (!this.canvas) {
            throw new Error('Game canvas not found');
        }

        // Initialize CreateJS stage
        this.stage = new createjs.Stage(this.canvas);
        this.stage.enableMouseOver(10);
        this.stage.mouseMoveOutside = true;

        // Enable touch events
        createjs.Touch.enable(this.stage);

        // Set canvas size and handle resize
        this.handleResize();
    }

    async initSystems() {
        // Initialize audio system
        this.audio = new AudioManager();
        await this.audio.init();

        // Initialize input system
        this.input = new InputManager(this.canvas);
        this.input.init();

        // Initialize game
        this.game = new Game(this.stage, this.audio, this.input);
        await this.game.init();

        // Initialize UI
        this.ui = new UIManager(this.game, this.audio);
        this.ui.init();

        // Initialize level editor
        this.levelEditor = new LevelEditor();
        this.levelEditor.init();
    }

    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', Utils.debounce(() => {
            this.handleResize();
            Utils.handleDeviceRotation();
        }, 250));

        // Orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleResize();
                Utils.handleDeviceRotation();
            }, 100);
        });

        // Visibility change (pause game when tab is hidden)
        document.addEventListener('visibilitychange', () => {
            if (this.game) {
                if (document.hidden) {
                    this.game.pause();
                } else {
                    // Don't auto-resume, let user click to resume
                }
            }
        });

        // Prevent context menu on canvas
        this.canvas.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });

        // Prevent scrolling on mobile
        document.addEventListener('touchmove', (e) => {
            if (e.target === this.canvas) {
                e.preventDefault();
            }
        }, { passive: false });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (!this.isInitialized) return;

            switch (e.code) {
                case 'Escape':
                    if (this.game && this.game.isPlaying()) {
                        this.game.pause();
                    }
                    break;
                case 'KeyM':
                    this.toggleMute();
                    break;
                case 'KeyF':
                    this.toggleFullscreen();
                    break;
                case 'KeyR':
                    if (this.game && this.game.isPlaying()) {
                        this.game.restartLevel();
                    }
                    break;
            }
        });

        // Fullscreen change
        document.addEventListener('fullscreenchange', () => {
            this.updateFullscreenButton();
        });
    }

    initUI() {
        // Main menu buttons
        document.getElementById('playBtn').addEventListener('click', () => {
            this.startGame();
        });

        document.getElementById('levelSelectBtn').addEventListener('click', () => {
            this.showLevelSelect();
        });

        document.getElementById('editorBtn').addEventListener('click', () => {
            this.showEditor();
        });

        document.getElementById('leaderboardBtn').addEventListener('click', () => {
            this.showLeaderboard();
        });

        document.getElementById('settingsBtn').addEventListener('click', () => {
            this.showSettings();
        });

        document.getElementById('howToPlayBtn').addEventListener('click', () => {
            this.showHowToPlay();
        });

        // Game controls
        document.getElementById('pauseBtn').addEventListener('click', () => {
            if (this.game) {
                this.game.togglePause();
            }
        });

        document.getElementById('muteBtn').addEventListener('click', () => {
            this.toggleMute();
        });

        document.getElementById('fullscreenBtn').addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // Back buttons
        document.getElementById('backToMenuBtn').addEventListener('click', () => {
            this.showMainMenu();
        });

        document.getElementById('backFromSettingsBtn').addEventListener('click', () => {
            this.showMainMenu();
        });

        document.getElementById('backFromHowToBtn').addEventListener('click', () => {
            this.showMainMenu();
        });

        // Settings
        this.setupSettingsListeners();

        // Generate level select grid
        this.generateLevelGrid();
    }

    setupSettingsListeners() {
        const soundToggle = document.getElementById('soundToggle');
        const musicToggle = document.getElementById('musicToggle');
        const cubeColor = document.getElementById('cubeColor');
        const cubeSize = document.getElementById('cubeSize');
        const swipeSensitivity = document.getElementById('swipeSensitivity');

        soundToggle.checked = this.settings.sound;
        musicToggle.checked = this.settings.music;
        cubeColor.value = this.settings.cubeColor;
        cubeSize.value = this.settings.cubeSize;
        swipeSensitivity.value = this.settings.swipeSensitivity;

        soundToggle.addEventListener('change', () => {
            this.settings.sound = soundToggle.checked;
            this.saveSettings();
            if (this.audio) {
                this.audio.setSoundEnabled(this.settings.sound);
            }
        });

        musicToggle.addEventListener('change', () => {
            this.settings.music = musicToggle.checked;
            this.saveSettings();
            if (this.audio) {
                this.audio.setMusicEnabled(this.settings.music);
            }
        });

        cubeColor.addEventListener('change', () => {
            this.settings.cubeColor = cubeColor.value;
            this.saveSettings();
            if (this.game) {
                this.game.updateCubeColor(this.settings.cubeColor);
            }
        });

        cubeSize.addEventListener('input', () => {
            this.settings.cubeSize = parseFloat(cubeSize.value);
            this.saveSettings();
            if (this.game) {
                this.game.updateCubeSize(this.settings.cubeSize);
            }
        });

        swipeSensitivity.addEventListener('input', () => {
            this.settings.swipeSensitivity = parseFloat(swipeSensitivity.value);
            this.saveSettings();
            if (this.input) {
                this.input.setSensitivity(this.settings.swipeSensitivity);
            }
        });
    }

    generateLevelGrid() {
        const levelGrid = document.getElementById('levelGrid');
        levelGrid.innerHTML = '';

        for (let i = 1; i <= 30; i++) {
            const button = document.createElement('button');
            button.className = 'level-btn';
            button.textContent = i;
            
            if (i <= this.gameData.unlockedLevels) {
                if (this.gameData.completedLevels.includes(i)) {
                    button.classList.add('completed');
                }
                button.addEventListener('click', () => {
                    this.startLevel(i);
                });
            } else {
                button.classList.add('locked');
                button.disabled = true;
            }

            levelGrid.appendChild(button);
        }
    }

    handleResize() {
        if (this.canvas) {
            const scale = Utils.resizeCanvas(this.canvas);
            if (this.stage) {
                this.stage.scaleX = this.stage.scaleY = scale;
            }
        }
    }

    updateProgress(progress) {
        this.loadingProgress = progress;
        Utils.updateLoadingProgress(progress);
    }

    showMainMenu() {
        this.hideAllScreens();
        document.getElementById('mainMenu').classList.remove('hidden');
    }

    showLevelSelect() {
        this.hideAllScreens();
        this.generateLevelGrid(); // Refresh grid
        document.getElementById('levelSelectScreen').classList.remove('hidden');
    }

    showSettings() {
        this.hideAllScreens();
        document.getElementById('settingsScreen').classList.remove('hidden');
    }

    showHowToPlay() {
        this.hideAllScreens();
        document.getElementById('howToPlayScreen').classList.remove('hidden');
    }

    showEditor() {
        if (this.levelEditor) {
            this.hideAllScreens();
            this.levelEditor.open();
        }
    }

    showLeaderboard() {
        // TODO: Implement leaderboard
        console.log('Leaderboard not yet implemented');
    }

    hideAllScreens() {
        const screens = [
            'mainMenu', 'levelSelectScreen', 'settingsScreen', 
            'howToPlayScreen', 'gameOverlay'
        ];
        screens.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.classList.add('hidden');
            }
        });
    }

    startGame() {
        this.startLevel(this.gameData.currentLevel);
    }

    startLevel(levelNumber) {
        this.hideAllScreens();
        if (this.game) {
            this.game.startLevel(levelNumber);
        }
    }

    toggleMute() {
        this.settings.sound = !this.settings.sound;
        this.settings.music = !this.settings.music;
        this.saveSettings();
        
        if (this.audio) {
            this.audio.setSoundEnabled(this.settings.sound);
            this.audio.setMusicEnabled(this.settings.music);
        }
        
        this.updateMuteButton();
    }

    updateMuteButton() {
        const muteBtn = document.getElementById('muteBtn');
        if (muteBtn) {
            muteBtn.textContent = (this.settings.sound && this.settings.music) ? '🔊' : '🔇';
        }
    }

    toggleFullscreen() {
        if (Utils.isFullscreen()) {
            Utils.exitFullscreen();
        } else {
            Utils.requestFullscreen();
        }
    }

    updateFullscreenButton() {
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = Utils.isFullscreen() ? '⛶' : '⛶';
        }
    }

    loadSettings() {
        const saved = Utils.loadFromStorage('cubeMove_settings', {});
        this.settings = { ...this.settings, ...saved };
    }

    saveSettings() {
        Utils.saveToStorage('cubeMove_settings', this.settings);
    }

    loadGameData() {
        const saved = Utils.loadFromStorage('cubeMove_gameData', {});
        this.gameData = { ...this.gameData, ...saved };
    }

    saveGameData() {
        Utils.saveToStorage('cubeMove_gameData', this.gameData);
    }

    showError(message) {
        alert(message); // TODO: Implement better error display
    }
}

// Initialize the game when the page loads
window.addEventListener('load', () => {
    const app = new GameApp();
    app.init();
    
    // Make app globally accessible for debugging
    window.gameApp = app;
});
