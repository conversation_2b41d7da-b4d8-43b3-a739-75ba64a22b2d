// Cube class for Cube Move game

class Cube {
    constructor(x, y, size = 40, color = '#FFD700') {
        this.x = x;
        this.y = y;
        this.size = size;
        this.color = color;
        this.baseSize = size;
        
        // Movement properties
        this.targetX = x;
        this.targetY = y;
        this.isMoving = false;
        this.moveSpeed = 8;
        this.moveProgress = 0;
        this.startX = x;
        this.startY = y;
        
        // Animation properties
        this.scale = 1.0;
        this.rotation = 0;
        this.animationTime = 0;
        this.bounceHeight = 0;
        this.shadowOffset = 5;
        
        // Visual effects
        this.glowIntensity = 0;
        this.trailParticles = [];
        this.maxTrailParticles = 8;
        
        // CreateJS display object
        this.container = new createjs.Container();
        this.shape = new createjs.Shape();
        this.shadow = new createjs.Shape();
        this.glow = new createjs.Shape();
        
        this.container.addChild(this.shadow);
        this.container.addChild(this.glow);
        this.container.addChild(this.shape);
        
        this.updateVisuals();
    }

    update(deltaTime) {
        this.animationTime += deltaTime;
        
        // Update movement
        if (this.isMoving) {
            this.updateMovement(deltaTime);
        }
        
        // Update animations
        this.updateAnimations(deltaTime);
        
        // Update trail particles
        this.updateTrail(deltaTime);
        
        // Update visual representation
        this.updateVisuals();
    }

    updateMovement(deltaTime) {
        if (!this.isMoving) return;
        
        this.moveProgress += this.moveSpeed * deltaTime;
        
        if (this.moveProgress >= 1.0) {
            // Movement complete
            this.moveProgress = 1.0;
            this.x = this.targetX;
            this.y = this.targetY;
            this.isMoving = false;
            this.onMoveComplete();
        } else {
            // Interpolate position with easing
            const easeProgress = Utils.easeOut(this.moveProgress);
            this.x = Utils.lerp(this.startX, this.targetX, easeProgress);
            this.y = Utils.lerp(this.startY, this.targetY, easeProgress);
            
            // Add bounce effect during movement
            const bounceProgress = Math.sin(this.moveProgress * Math.PI);
            this.bounceHeight = bounceProgress * 10;
        }
    }

    updateAnimations(deltaTime) {
        // Idle floating animation
        if (!this.isMoving) {
            this.bounceHeight = Math.sin(this.animationTime * 2) * 3;
            this.rotation = Math.sin(this.animationTime * 1.5) * 2;
        }
        
        // Glow pulse
        this.glowIntensity = (Math.sin(this.animationTime * 3) + 1) * 0.5;
        
        // Scale pulse when moving
        if (this.isMoving) {
            this.scale = 1.0 + Math.sin(this.moveProgress * Math.PI * 2) * 0.1;
        } else {
            this.scale = 1.0 + Math.sin(this.animationTime * 4) * 0.05;
        }
    }

    updateTrail(deltaTime) {
        // Add new trail particle if moving
        if (this.isMoving && this.trailParticles.length < this.maxTrailParticles) {
            this.trailParticles.push({
                x: this.x,
                y: this.y,
                life: 1.0,
                maxLife: 1.0,
                size: this.size * 0.3
            });
        }
        
        // Update existing particles
        for (let i = this.trailParticles.length - 1; i >= 0; i--) {
            const particle = this.trailParticles[i];
            particle.life -= deltaTime * 2;
            
            if (particle.life <= 0) {
                this.trailParticles.splice(i, 1);
            }
        }
    }

    updateVisuals() {
        // Clear previous drawings
        this.shape.graphics.clear();
        this.shadow.graphics.clear();
        this.glow.graphics.clear();
        
        // Draw shadow
        this.drawShadow();
        
        // Draw glow effect
        this.drawGlow();
        
        // Draw main cube
        this.drawCube();
        
        // Draw trail particles
        this.drawTrail();
        
        // Update container position
        this.container.x = this.x;
        this.container.y = this.y - this.bounceHeight;
        this.container.rotation = this.rotation;
        this.container.scaleX = this.container.scaleY = this.scale;
    }

    drawShadow() {
        const shadowSize = this.size * 0.8;
        const shadowAlpha = 0.3;
        
        this.shadow.graphics
            .beginFill(`rgba(0, 0, 0, ${shadowAlpha})`)
            .drawEllipse(-shadowSize/2, this.shadowOffset, shadowSize, shadowSize * 0.5);
    }

    drawGlow() {
        if (this.glowIntensity <= 0) return;
        
        const glowSize = this.size * (1.2 + this.glowIntensity * 0.3);
        const glowAlpha = this.glowIntensity * 0.3;
        
        // Create radial gradient effect
        const rgb = Utils.hexToRgb(this.color);
        if (rgb) {
            this.glow.graphics
                .beginRadialGradientFill(
                    [`rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${glowAlpha})`, 'rgba(255, 255, 255, 0)'],
                    [0, 1],
                    0, 0, 0,
                    0, 0, glowSize/2
                )
                .drawCircle(0, 0, glowSize/2);
        }
    }

    drawCube() {
        const halfSize = this.size / 2;
        
        // Main cube body
        this.shape.graphics
            .beginFill(this.color)
            .drawRoundRect(-halfSize, -halfSize, this.size, this.size, 8);
        
        // Highlight
        const highlightColor = this.lightenColor(this.color, 0.3);
        this.shape.graphics
            .beginFill(highlightColor)
            .drawRoundRect(-halfSize, -halfSize, this.size, this.size * 0.3, 8);
        
        // Border
        this.shape.graphics
            .setStrokeStyle(2)
            .beginStroke(this.darkenColor(this.color, 0.2))
            .drawRoundRect(-halfSize, -halfSize, this.size, this.size, 8);
    }

    drawTrail() {
        this.trailParticles.forEach(particle => {
            const alpha = particle.life / particle.maxLife;
            const size = particle.size * alpha;
            const rgb = Utils.hexToRgb(this.color);
            
            if (rgb) {
                this.shape.graphics
                    .beginFill(`rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${alpha * 0.6})`)
                    .drawCircle(particle.x - this.x, particle.y - this.y, size/2);
            }
        });
    }

    // Movement methods
    moveTo(targetX, targetY, callback = null) {
        if (this.isMoving) return false;
        
        this.startX = this.x;
        this.startY = this.y;
        this.targetX = targetX;
        this.targetY = targetY;
        this.isMoving = true;
        this.moveProgress = 0;
        this.moveCallback = callback;
        
        return true;
    }

    moveByDirection(direction, distance = 60) {
        let targetX = this.x;
        let targetY = this.y;
        
        switch (direction) {
            case 'up':
                targetY -= distance;
                break;
            case 'down':
                targetY += distance;
                break;
            case 'left':
                targetX -= distance;
                break;
            case 'right':
                targetX += distance;
                break;
        }
        
        return this.moveTo(targetX, targetY);
    }

    onMoveComplete() {
        this.bounceHeight = 0;
        this.rotation = 0;
        
        if (this.moveCallback) {
            this.moveCallback();
            this.moveCallback = null;
        }
    }

    // Appearance methods
    setColor(color) {
        this.color = color;
    }

    setSize(size) {
        this.size = this.baseSize * size;
    }

    // Utility methods for color manipulation
    lightenColor(color, amount) {
        const rgb = Utils.hexToRgb(color);
        if (!rgb) return color;
        
        const r = Math.min(255, Math.floor(rgb.r + (255 - rgb.r) * amount));
        const g = Math.min(255, Math.floor(rgb.g + (255 - rgb.g) * amount));
        const b = Math.min(255, Math.floor(rgb.b + (255 - rgb.b) * amount));
        
        return Utils.rgbToHex(r, g, b);
    }

    darkenColor(color, amount) {
        const rgb = Utils.hexToRgb(color);
        if (!rgb) return color;
        
        const r = Math.floor(rgb.r * (1 - amount));
        const g = Math.floor(rgb.g * (1 - amount));
        const b = Math.floor(rgb.b * (1 - amount));
        
        return Utils.rgbToHex(r, g, b);
    }

    // Animation methods
    playBounceAnimation() {
        const originalScale = this.scale;
        const bounceScale = 1.3;
        const duration = 300;
        
        createjs.Tween.get(this.container)
            .to({scaleX: bounceScale, scaleY: bounceScale}, duration/2, createjs.Ease.quadOut)
            .to({scaleX: originalScale, scaleY: originalScale}, duration/2, createjs.Ease.quadIn);
    }

    playShakeAnimation() {
        const originalX = this.container.x;
        const shakeAmount = 5;
        const duration = 200;
        
        createjs.Tween.get(this.container)
            .to({x: originalX - shakeAmount}, duration/4)
            .to({x: originalX + shakeAmount}, duration/4)
            .to({x: originalX - shakeAmount}, duration/4)
            .to({x: originalX}, duration/4);
    }

    // State methods
    getPosition() {
        return { x: this.x, y: this.y };
    }

    getBounds() {
        const halfSize = this.size / 2;
        return {
            x: this.x - halfSize,
            y: this.y - halfSize,
            width: this.size,
            height: this.size
        };
    }

    isAt(x, y, tolerance = 5) {
        return Utils.distance(this.x, this.y, x, y) <= tolerance;
    }

    // Cleanup
    destroy() {
        if (this.container.parent) {
            this.container.parent.removeChild(this.container);
        }
        
        createjs.Tween.removeTweens(this.container);
        this.trailParticles = [];
        this.moveCallback = null;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Cube;
}
