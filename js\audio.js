// Audio management system for Cube Move game

class AudioManager {
    constructor() {
        this.sounds = {};
        this.music = {};
        this.soundEnabled = true;
        this.musicEnabled = true;
        this.masterVolume = 1.0;
        this.soundVolume = 0.7;
        this.musicVolume = 0.5;
        this.currentMusic = null;
        this.isInitialized = false;
        
        // Audio context for better control
        this.audioContext = null;
        this.gainNode = null;
        
        // Preload list
        this.soundList = {
            'move': 'assets/audio/move.mp3',
            'success': 'assets/audio/success.mp3',
            'fail': 'assets/audio/fail.mp3',
            'button': 'assets/audio/button.mp3',
            'levelComplete': 'assets/audio/level_complete.mp3',
            'gameOver': 'assets/audio/game_over.mp3'
        };
        
        this.musicList = {
            'menu': 'assets/audio/menu_music.mp3',
            'game': 'assets/audio/game_music.mp3',
            'victory': 'assets/audio/victory_music.mp3'
        };
    }

    async init() {
        try {
            // Initialize Web Audio API if available
            if (window.AudioContext || window.webkitAudioContext) {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                this.gainNode = this.audioContext.createGain();
                this.gainNode.connect(this.audioContext.destination);
            }

            // Load all audio files
            await this.loadAllAudio();
            this.isInitialized = true;
            
            console.log('Audio system initialized successfully');
        } catch (error) {
            console.warn('Audio initialization failed:', error);
            // Continue without audio
            this.isInitialized = true;
        }
    }

    async loadAllAudio() {
        const loadPromises = [];
        
        // Load sound effects
        for (const [name, url] of Object.entries(this.soundList)) {
            loadPromises.push(this.loadSound(name, url));
        }
        
        // Load music
        for (const [name, url] of Object.entries(this.musicList)) {
            loadPromises.push(this.loadMusic(name, url));
        }
        
        await Promise.allSettled(loadPromises);
    }

    async loadSound(name, url) {
        return new Promise((resolve, reject) => {
            const audio = new Audio();
            audio.preload = 'auto';
            
            audio.addEventListener('canplaythrough', () => {
                this.sounds[name] = audio;
                resolve();
            });
            
            audio.addEventListener('error', (e) => {
                console.warn(`Failed to load sound: ${name}`, e);
                resolve(); // Don't reject, just continue without this sound
            });
            
            audio.src = url;
        });
    }

    async loadMusic(name, url) {
        return new Promise((resolve, reject) => {
            const audio = new Audio();
            audio.preload = 'auto';
            audio.loop = true;
            
            audio.addEventListener('canplaythrough', () => {
                this.music[name] = audio;
                resolve();
            });
            
            audio.addEventListener('error', (e) => {
                console.warn(`Failed to load music: ${name}`, e);
                resolve(); // Don't reject, just continue without this music
            });
            
            audio.src = url;
        });
    }

    playSound(name, volume = 1.0, pitch = 1.0) {
        if (!this.soundEnabled || !this.sounds[name]) return;
        
        try {
            const sound = this.sounds[name].cloneNode();
            sound.volume = this.soundVolume * volume * this.masterVolume;
            
            // Apply pitch if Web Audio API is available
            if (this.audioContext && pitch !== 1.0) {
                // Create audio nodes for pitch shifting
                const source = this.audioContext.createMediaElementSource(sound);
                const gainNode = this.audioContext.createGain();
                gainNode.gain.value = sound.volume;
                
                source.connect(gainNode);
                gainNode.connect(this.audioContext.destination);
                
                // Note: Pitch shifting requires additional libraries for full support
                // For now, we'll just play at normal pitch
            }
            
            sound.play().catch(e => {
                console.warn(`Failed to play sound: ${name}`, e);
            });
            
            return sound;
        } catch (error) {
            console.warn(`Error playing sound: ${name}`, error);
        }
    }

    playMusic(name, fadeIn = false, fadeTime = 1000) {
        if (!this.musicEnabled || !this.music[name]) return;
        
        try {
            // Stop current music
            this.stopMusic(fadeIn ? fadeTime : 0);
            
            const music = this.music[name];
            music.volume = fadeIn ? 0 : this.musicVolume * this.masterVolume;
            music.currentTime = 0;
            
            this.currentMusic = music;
            
            music.play().then(() => {
                if (fadeIn) {
                    this.fadeIn(music, this.musicVolume * this.masterVolume, fadeTime);
                }
            }).catch(e => {
                console.warn(`Failed to play music: ${name}`, e);
            });
            
        } catch (error) {
            console.warn(`Error playing music: ${name}`, error);
        }
    }

    stopMusic(fadeOut = 0) {
        if (!this.currentMusic) return;
        
        if (fadeOut > 0) {
            this.fadeOut(this.currentMusic, fadeOut, () => {
                this.currentMusic.pause();
                this.currentMusic = null;
            });
        } else {
            this.currentMusic.pause();
            this.currentMusic = null;
        }
    }

    pauseMusic() {
        if (this.currentMusic && !this.currentMusic.paused) {
            this.currentMusic.pause();
        }
    }

    resumeMusic() {
        if (this.currentMusic && this.currentMusic.paused) {
            this.currentMusic.play().catch(e => {
                console.warn('Failed to resume music', e);
            });
        }
    }

    fadeIn(audio, targetVolume, duration) {
        const startVolume = 0;
        const volumeStep = targetVolume / (duration / 50);
        let currentVolume = startVolume;
        
        const fadeInterval = setInterval(() => {
            currentVolume += volumeStep;
            if (currentVolume >= targetVolume) {
                currentVolume = targetVolume;
                clearInterval(fadeInterval);
            }
            audio.volume = currentVolume;
        }, 50);
    }

    fadeOut(audio, duration, callback) {
        const startVolume = audio.volume;
        const volumeStep = startVolume / (duration / 50);
        let currentVolume = startVolume;
        
        const fadeInterval = setInterval(() => {
            currentVolume -= volumeStep;
            if (currentVolume <= 0) {
                currentVolume = 0;
                clearInterval(fadeInterval);
                if (callback) callback();
            }
            audio.volume = currentVolume;
        }, 50);
    }

    // Volume controls
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        this.updateAllVolumes();
    }

    setSoundVolume(volume) {
        this.soundVolume = Math.max(0, Math.min(1, volume));
        this.updateAllVolumes();
    }

    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        this.updateAllVolumes();
    }

    updateAllVolumes() {
        if (this.currentMusic) {
            this.currentMusic.volume = this.musicVolume * this.masterVolume;
        }
    }

    // Enable/disable controls
    setSoundEnabled(enabled) {
        this.soundEnabled = enabled;
        if (!enabled) {
            // Stop all currently playing sounds
            Object.values(this.sounds).forEach(sound => {
                if (!sound.paused) {
                    sound.pause();
                }
            });
        }
    }

    setMusicEnabled(enabled) {
        this.musicEnabled = enabled;
        if (!enabled) {
            this.stopMusic();
        } else if (this.currentMusic && this.currentMusic.paused) {
            this.resumeMusic();
        }
    }

    // Convenience methods for common game sounds
    playMoveSound() {
        this.playSound('move', 0.5);
    }

    playSuccessSound() {
        this.playSound('success', 0.8);
    }

    playFailSound() {
        this.playSound('fail', 0.6);
    }

    playButtonSound() {
        this.playSound('button', 0.4);
    }

    playLevelCompleteSound() {
        this.playSound('levelComplete', 1.0);
    }

    playGameOverSound() {
        this.playSound('gameOver', 0.8);
    }

    // Music control methods
    playMenuMusic() {
        this.playMusic('menu', true, 1500);
    }

    playGameMusic() {
        this.playMusic('game', true, 2000);
    }

    playVictoryMusic() {
        this.playMusic('victory', false);
    }

    // Get audio info
    isSoundEnabled() {
        return this.soundEnabled;
    }

    isMusicEnabled() {
        return this.musicEnabled;
    }

    isPlayingMusic() {
        return this.currentMusic && !this.currentMusic.paused;
    }

    getCurrentMusicName() {
        if (!this.currentMusic) return null;
        
        for (const [name, audio] of Object.entries(this.music)) {
            if (audio === this.currentMusic) {
                return name;
            }
        }
        return null;
    }

    // Audio context management (for mobile)
    resumeAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
    }

    // Cleanup
    destroy() {
        this.stopMusic();
        
        // Stop all sounds
        Object.values(this.sounds).forEach(sound => {
            if (!sound.paused) {
                sound.pause();
            }
        });
        
        // Clear references
        this.sounds = {};
        this.music = {};
        this.currentMusic = null;
        
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
    }
}

// Create placeholder audio files (since we don't have actual audio files)
// This would normally be handled by your build process
AudioManager.createPlaceholderAudio = function() {
    // Create silent audio data URLs for development
    const silentAudio = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
    
    return {
        'move': silentAudio,
        'success': silentAudio,
        'fail': silentAudio,
        'button': silentAudio,
        'levelComplete': silentAudio,
        'gameOver': silentAudio,
        'menu': silentAudio,
        'game': silentAudio,
        'victory': silentAudio
    };
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AudioManager;
}
