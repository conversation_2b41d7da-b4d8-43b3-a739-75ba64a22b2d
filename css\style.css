/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Game Container */
#gameContainer {
    position: relative;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

#gameCanvas {
    background: #1a1a2e;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 100vw;
    max-height: 100vh;
    object-fit: contain;
}

/* Loading Screen */
#loadingScreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-container {
    text-align: center;
    color: white;
}

.cube-loader {
    width: 60px;
    height: 60px;
    background: #FFD700;
    margin: 0 auto 20px;
    animation: cubeRotate 2s infinite linear;
    border-radius: 8px;
}

@keyframes cubeRotate {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.2); }
    100% { transform: rotate(360deg) scale(1); }
}

.loading-text {
    font-size: 24px;
    margin-bottom: 20px;
    font-weight: bold;
}

.loading-progress {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
}

.progress-bar {
    height: 100%;
    background: #FFD700;
    width: 0%;
    transition: width 0.3s ease;
}

/* Browser Not Supported */
#browserNotSupported {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #1a1a2e;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1001;
}

.error-container {
    text-align: center;
    padding: 40px;
    max-width: 500px;
}

.error-container h2 {
    color: #ff6b6b;
    margin-bottom: 20px;
    font-size: 28px;
}

.supported-browsers {
    margin-top: 30px;
    text-align: left;
}

.supported-browsers ul {
    list-style: none;
    padding-left: 20px;
}

.supported-browsers li {
    margin: 5px 0;
    padding-left: 20px;
    position: relative;
}

.supported-browsers li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #4ecdc4;
}

/* Rotate Device */
.rotate-device {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #1a1a2e;
    color: white;
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 999;
}

.rotate-container {
    text-align: center;
}

.rotate-icon {
    width: 80px;
    height: 80px;
    border: 3px solid white;
    border-radius: 10px;
    margin: 0 auto 20px;
    position: relative;
    animation: rotatePhone 2s infinite ease-in-out;
}

.rotate-icon:before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 30px;
    border: 2px solid white;
    border-radius: 3px;
}

@keyframes rotatePhone {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(90deg); }
}

/* Game UI */
#gameUI {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.top-ui {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    pointer-events: auto;
}

.level-info {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 16px;
}

.game-controls {
    display: flex;
    gap: 10px;
}

.ui-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.ui-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

/* Overlays */
.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: auto;
}

.overlay-content {
    background: white;
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.overlay-content h2 {
    color: #333;
    margin-bottom: 15px;
    font-size: 28px;
}

.overlay-content p {
    color: #666;
    margin-bottom: 25px;
    font-size: 16px;
}

.overlay-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Menu Screens */
.menu-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: auto;
}

.menu-container {
    text-align: center;
    color: white;
    max-width: 400px;
    width: 90%;
}

.game-logo h1 {
    font-size: 48px;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 3px;
}

.game-logo p {
    font-size: 18px;
    margin-bottom: 40px;
    opacity: 0.9;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Buttons */
.menu-btn, .game-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #333;
}

.menu-btn:hover, .game-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.game-btn {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
}

/* Hidden class */
.hidden {
    display: none !important;
}

/* Level Grid */
.level-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 10px;
    margin: 30px 0;
    max-height: 400px;
    overflow-y: auto;
}

.level-btn {
    width: 60px;
    height: 60px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
}

.level-btn:hover {
    transform: scale(1.1);
}

.level-btn.completed {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #333;
}

.level-btn.locked {
    background: #666;
    cursor: not-allowed;
    opacity: 0.5;
}

/* Settings */
.settings-group {
    margin: 30px 0;
    text-align: left;
}

.settings-group h3 {
    margin-bottom: 15px;
    font-size: 20px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
    padding-bottom: 5px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.setting-item input {
    margin-left: 10px;
}

/* Instructions */
.instructions {
    text-align: left;
    margin: 30px 0;
}

.instruction-item {
    display: flex;
    align-items: center;
    margin: 20px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.instruction-icon {
    font-size: 30px;
    margin-right: 20px;
    width: 50px;
    text-align: center;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    max-width: 400px;
    width: 90%;
    position: relative;
}

.close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 28px;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

.share-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-top: 20px;
}

.share-btn {
    padding: 10px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.share-btn.facebook { background: #3b5998; color: white; }
.share-btn.twitter { background: #1da1f2; color: white; }
.share-btn.whatsapp { background: #25d366; color: white; }
.share-btn.telegram { background: #0088cc; color: white; }
.share-btn.reddit { background: #ff4500; color: white; }
.share-btn.linkedin { background: #0077b5; color: white; }

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-logo h1 {
        font-size: 36px;
    }
    
    .menu-btn, .game-btn {
        font-size: 16px;
        padding: 12px 25px;
    }
    
    .top-ui {
        top: 10px;
        left: 10px;
        right: 10px;
    }
    
    .ui-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
    
    .level-info {
        font-size: 14px;
        padding: 8px 12px;
    }
}

@media (orientation: landscape) and (max-height: 500px) {
    .rotate-device {
        display: flex !important;
    }
    
    #gameContainer {
        display: none !important;
    }
}

/* Level Editor Styles */
.level-editor {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 2000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.editor-container {
    background: white;
    border-radius: 15px;
    width: 90%;
    height: 90%;
    max-width: 1200px;
    max-height: 800px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.editor-header {
    background: #667eea;
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.editor-header h2 {
    margin: 0;
    font-size: 24px;
}

.editor-controls {
    display: flex;
    gap: 10px;
}

.editor-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    font-weight: bold;
    transition: background 0.3s ease;
}

.editor-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.editor-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.editor-tools {
    width: 250px;
    background: #f8f9fa;
    padding: 20px;
    border-right: 1px solid #dee2e6;
    overflow-y: auto;
}

.editor-tools h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
}

.tool-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 30px;
}

.tool-btn {
    padding: 10px;
    border: 2px solid transparent;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.tool-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.tool-btn.active {
    border-color: #333;
    transform: scale(1.05);
}

.editor-settings {
    margin-top: 20px;
}

.editor-settings label {
    display: block;
    margin: 15px 0;
    color: #333;
    font-weight: bold;
}

.editor-settings input[type="range"] {
    width: 100%;
    margin: 5px 0;
}

.editor-settings input[type="color"] {
    width: 50px;
    height: 30px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.editor-canvas-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    overflow: auto;
}

#editorCanvas {
    border: 2px solid #dee2e6;
    border-radius: 10px;
    background: #1a1a2e;
    cursor: crosshair;
}

.editor-instructions {
    margin-top: 20px;
    text-align: center;
    color: #666;
}

.editor-instructions p {
    margin: 5px 0;
    font-size: 14px;
}

/* Notification Styles */
.notification {
    font-family: Arial, sans-serif;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Small Screen Adjustments */
.small-screen .menu-btn,
.small-screen .game-btn {
    font-size: 14px;
    padding: 10px 20px;
}

.small-screen .game-logo h1 {
    font-size: 32px;
}

.small-screen .level-btn {
    width: 50px;
    height: 50px;
    font-size: 14px;
}

.small-screen .editor-content {
    flex-direction: column;
}

.small-screen .editor-tools {
    width: 100%;
    max-height: 200px;
}

.small-screen .tool-buttons {
    grid-template-columns: repeat(3, 1fr);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.menu-screen {
    animation: fadeIn 0.5s ease;
}

.overlay {
    animation: fadeIn 0.3s ease;
}

.level-editor {
    animation: fadeIn 0.3s ease;
}
