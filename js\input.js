// Input management system for Cube Move game

class InputManager {
    constructor(canvas) {
        this.canvas = canvas;
        this.isEnabled = true;
        this.sensitivity = 1.0;
        
        // Touch/mouse state
        this.isPointerDown = false;
        this.startX = 0;
        this.startY = 0;
        this.currentX = 0;
        this.currentY = 0;
        this.lastMoveTime = 0;
        
        // Swipe detection
        this.minSwipeDistance = 30;
        this.maxSwipeTime = 500;
        this.swipeStartTime = 0;
        
        // Keyboard state
        this.keys = {};
        this.keyBindings = {
            'ArrowUp': 'up',
            'ArrowDown': 'down',
            'ArrowLeft': 'left',
            'ArrowRight': 'right',
            'KeyW': 'up',
            'KeyS': 'down',
            'KeyA': 'left',
            'KeyD': 'right'
        };
        
        // Event callbacks
        this.onMove = null;
        this.onSwipe = null;
        this.onTap = null;
        this.onKeyPress = null;
        
        // Bind methods
        this.handlePointerDown = this.handlePointerDown.bind(this);
        this.handlePointerMove = this.handlePointerMove.bind(this);
        this.handlePointerUp = this.handlePointerUp.bind(this);
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.handleKeyUp = this.handleKeyUp.bind(this);
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Mouse events
        this.canvas.addEventListener('mousedown', this.handlePointerDown);
        this.canvas.addEventListener('mousemove', this.handlePointerMove);
        this.canvas.addEventListener('mouseup', this.handlePointerUp);
        this.canvas.addEventListener('mouseleave', this.handlePointerUp);

        // Touch events
        this.canvas.addEventListener('touchstart', this.handlePointerDown, { passive: false });
        this.canvas.addEventListener('touchmove', this.handlePointerMove, { passive: false });
        this.canvas.addEventListener('touchend', this.handlePointerUp, { passive: false });
        this.canvas.addEventListener('touchcancel', this.handlePointerUp, { passive: false });

        // Keyboard events
        document.addEventListener('keydown', this.handleKeyDown);
        document.addEventListener('keyup', this.handleKeyUp);

        // Prevent default behaviors
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
        this.canvas.addEventListener('selectstart', (e) => e.preventDefault());
    }

    handlePointerDown(event) {
        if (!this.isEnabled) return;

        event.preventDefault();
        
        const point = this.getEventPoint(event);
        this.isPointerDown = true;
        this.startX = this.currentX = point.x;
        this.startY = this.currentY = point.y;
        this.swipeStartTime = Date.now();
        this.lastMoveTime = this.swipeStartTime;
    }

    handlePointerMove(event) {
        if (!this.isEnabled || !this.isPointerDown) return;

        event.preventDefault();
        
        const point = this.getEventPoint(event);
        this.currentX = point.x;
        this.currentY = point.y;
        this.lastMoveTime = Date.now();

        // Call move callback if set
        if (this.onMove) {
            const deltaX = this.currentX - this.startX;
            const deltaY = this.currentY - this.startY;
            this.onMove(deltaX, deltaY, this.currentX, this.currentY);
        }
    }

    handlePointerUp(event) {
        if (!this.isEnabled || !this.isPointerDown) return;

        event.preventDefault();
        
        const point = this.getEventPoint(event);
        this.currentX = point.x;
        this.currentY = point.y;
        
        const deltaX = this.currentX - this.startX;
        const deltaY = this.currentY - this.startY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const duration = Date.now() - this.swipeStartTime;

        // Check for swipe
        if (distance >= this.minSwipeDistance * this.sensitivity && duration <= this.maxSwipeTime) {
            const direction = this.getSwipeDirection(deltaX, deltaY);
            if (direction && this.onSwipe) {
                this.onSwipe(direction, distance, duration);
            }
        } else if (distance < this.minSwipeDistance && duration < 300) {
            // Tap/click
            if (this.onTap) {
                this.onTap(this.currentX, this.currentY);
            }
        }

        this.isPointerDown = false;
    }

    handleKeyDown(event) {
        if (!this.isEnabled) return;

        const action = this.keyBindings[event.code];
        if (action) {
            event.preventDefault();
            
            if (!this.keys[action]) {
                this.keys[action] = true;
                
                if (this.onKeyPress) {
                    this.onKeyPress(action, true);
                }
                
                // Treat as swipe for movement
                if (this.onSwipe) {
                    this.onSwipe(action, 1, 0);
                }
            }
        }
    }

    handleKeyUp(event) {
        if (!this.isEnabled) return;

        const action = this.keyBindings[event.code];
        if (action) {
            event.preventDefault();
            this.keys[action] = false;
            
            if (this.onKeyPress) {
                this.onKeyPress(action, false);
            }
        }
    }

    getEventPoint(event) {
        const rect = this.canvas.getBoundingClientRect();
        let clientX, clientY;

        if (event.touches && event.touches.length > 0) {
            clientX = event.touches[0].clientX;
            clientY = event.touches[0].clientY;
        } else {
            clientX = event.clientX;
            clientY = event.clientY;
        }

        return {
            x: clientX - rect.left,
            y: clientY - rect.top
        };
    }

    getSwipeDirection(deltaX, deltaY) {
        const absDeltaX = Math.abs(deltaX);
        const absDeltaY = Math.abs(deltaY);

        // Determine primary direction
        if (absDeltaX > absDeltaY) {
            // Horizontal swipe
            return deltaX > 0 ? 'right' : 'left';
        } else {
            // Vertical swipe
            return deltaY > 0 ? 'down' : 'up';
        }
    }

    // Public methods
    setEnabled(enabled) {
        this.isEnabled = enabled;
        if (!enabled) {
            this.isPointerDown = false;
            this.keys = {};
        }
    }

    setSensitivity(sensitivity) {
        this.sensitivity = Math.max(0.1, Math.min(3.0, sensitivity));
    }

    isKeyPressed(action) {
        return !!this.keys[action];
    }

    setMoveCallback(callback) {
        this.onMove = callback;
    }

    setSwipeCallback(callback) {
        this.onSwipe = callback;
    }

    setTapCallback(callback) {
        this.onTap = callback;
    }

    setKeyCallback(callback) {
        this.onKeyPress = callback;
    }

    // Get current pointer position relative to canvas
    getPointerPosition() {
        return {
            x: this.currentX,
            y: this.currentY,
            isDown: this.isPointerDown
        };
    }

    // Check if currently swiping
    isSwiping() {
        if (!this.isPointerDown) return false;
        
        const deltaX = this.currentX - this.startX;
        const deltaY = this.currentY - this.startY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        
        return distance >= this.minSwipeDistance * this.sensitivity;
    }

    // Get current swipe info
    getSwipeInfo() {
        if (!this.isPointerDown) return null;
        
        const deltaX = this.currentX - this.startX;
        const deltaY = this.currentY - this.startY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const duration = Date.now() - this.swipeStartTime;
        
        return {
            deltaX,
            deltaY,
            distance,
            duration,
            direction: this.getSwipeDirection(deltaX, deltaY)
        };
    }

    // Vibrate on mobile devices
    vibrate(pattern = 50) {
        if (navigator.vibrate) {
            navigator.vibrate(pattern);
        }
    }

    // Reset input state
    reset() {
        this.isPointerDown = false;
        this.keys = {};
        this.startX = this.startY = 0;
        this.currentX = this.currentY = 0;
    }

    // Cleanup
    destroy() {
        // Remove mouse events
        this.canvas.removeEventListener('mousedown', this.handlePointerDown);
        this.canvas.removeEventListener('mousemove', this.handlePointerMove);
        this.canvas.removeEventListener('mouseup', this.handlePointerUp);
        this.canvas.removeEventListener('mouseleave', this.handlePointerUp);

        // Remove touch events
        this.canvas.removeEventListener('touchstart', this.handlePointerDown);
        this.canvas.removeEventListener('touchmove', this.handlePointerMove);
        this.canvas.removeEventListener('touchend', this.handlePointerUp);
        this.canvas.removeEventListener('touchcancel', this.handlePointerUp);

        // Remove keyboard events
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);

        // Clear callbacks
        this.onMove = null;
        this.onSwipe = null;
        this.onTap = null;
        this.onKeyPress = null;
    }
}

// Direction constants
InputManager.DIRECTIONS = {
    UP: 'up',
    DOWN: 'down',
    LEFT: 'left',
    RIGHT: 'right'
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = InputManager;
}
