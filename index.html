<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>Cube Move - HTML5 Game</title>
    
    <!-- Preload CreateJS -->
    <script src="https://code.createjs.com/1.0.0/createjs.min.js"></script>
    
    <!-- Game Styles -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/images/favicon.png">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loadingScreen">
        <div class="loading-container">
            <div class="cube-loader"></div>
            <div class="loading-text">Loading Cube Move...</div>
            <div class="loading-progress">
                <div class="progress-bar"></div>
            </div>
        </div>
    </div>

    <!-- Browser Not Supported -->
    <div id="browserNotSupported" style="display: none;">
        <div class="error-container">
            <h2>Browser Not Supported</h2>
            <p>Your browser doesn't support HTML5 Canvas. Please update your browser or try a different one.</p>
            <div class="supported-browsers">
                <p>Supported browsers:</p>
                <ul>
                    <li>Chrome 30+</li>
                    <li>Firefox 25+</li>
                    <li>Safari 7+</li>
                    <li>Edge 12+</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Mobile Rotate Instruction -->
    <div id="rotateDevice" class="rotate-device">
        <div class="rotate-container">
            <div class="rotate-icon"></div>
            <p>Please rotate your device to portrait mode</p>
        </div>
    </div>

    <!-- Main Game Container -->
    <div id="gameContainer">
        <!-- Game Canvas -->
        <canvas id="gameCanvas" width="768" height="1024"></canvas>
        
        <!-- Game UI Overlay -->
        <div id="gameUI">
            <!-- Top UI -->
            <div class="top-ui">
                <div class="level-info">
                    <span id="currentLevel">Level 1</span>
                </div>
                <div class="game-controls">
                    <button id="pauseBtn" class="ui-btn">⏸</button>
                    <button id="muteBtn" class="ui-btn">🔊</button>
                    <button id="fullscreenBtn" class="ui-btn">⛶</button>
                </div>
            </div>

            <!-- Game Over Overlay -->
            <div id="gameOverlay" class="overlay hidden">
                <div class="overlay-content">
                    <h2 id="overlayTitle">Level Complete!</h2>
                    <p id="overlayMessage">Great job! You completed the level.</p>
                    <div class="overlay-buttons">
                        <button id="nextLevelBtn" class="game-btn">Next Level</button>
                        <button id="retryBtn" class="game-btn">Retry</button>
                        <button id="menuBtn" class="game-btn">Menu</button>
                    </div>
                </div>
            </div>

            <!-- Main Menu -->
            <div id="mainMenu" class="menu-screen">
                <div class="menu-container">
                    <div class="game-logo">
                        <h1>CUBE MOVE</h1>
                        <p>Navigate the cube to victory!</p>
                    </div>
                    <div class="menu-buttons">
                        <button id="playBtn" class="menu-btn">Play Game</button>
                        <button id="levelSelectBtn" class="menu-btn">Level Select</button>
                        <button id="editorBtn" class="menu-btn">Level Editor</button>
                        <button id="leaderboardBtn" class="menu-btn">Leaderboard</button>
                        <button id="settingsBtn" class="menu-btn">Settings</button>
                        <button id="howToPlayBtn" class="menu-btn">How to Play</button>
                    </div>
                </div>
            </div>

            <!-- Level Select Screen -->
            <div id="levelSelectScreen" class="menu-screen hidden">
                <div class="menu-container">
                    <h2>Select Level</h2>
                    <div id="levelGrid" class="level-grid">
                        <!-- Level buttons will be generated here -->
                    </div>
                    <button id="backToMenuBtn" class="menu-btn">Back to Menu</button>
                </div>
            </div>

            <!-- Settings Screen -->
            <div id="settingsScreen" class="menu-screen hidden">
                <div class="menu-container">
                    <h2>Settings</h2>
                    <div class="settings-group">
                        <h3>Audio</h3>
                        <label class="setting-item">
                            <span>Sound Effects</span>
                            <input type="checkbox" id="soundToggle" checked>
                        </label>
                        <label class="setting-item">
                            <span>Background Music</span>
                            <input type="checkbox" id="musicToggle" checked>
                        </label>
                    </div>
                    <div class="settings-group">
                        <h3>Cube Appearance</h3>
                        <label class="setting-item">
                            <span>Cube Color</span>
                            <input type="color" id="cubeColor" value="#FFD700">
                        </label>
                        <label class="setting-item">
                            <span>Cube Size</span>
                            <input type="range" id="cubeSize" min="0.5" max="2" step="0.1" value="1">
                        </label>
                    </div>
                    <div class="settings-group">
                        <h3>Controls</h3>
                        <label class="setting-item">
                            <span>Swipe Sensitivity</span>
                            <input type="range" id="swipeSensitivity" min="0.5" max="2" step="0.1" value="1">
                        </label>
                    </div>
                    <button id="backFromSettingsBtn" class="menu-btn">Back</button>
                </div>
            </div>

            <!-- How to Play Screen -->
            <div id="howToPlayScreen" class="menu-screen hidden">
                <div class="menu-container">
                    <h2>How to Play</h2>
                    <div class="instructions">
                        <div class="instruction-item">
                            <div class="instruction-icon">👆</div>
                            <p>Swipe in any direction to move the yellow cube</p>
                        </div>
                        <div class="instruction-item">
                            <div class="instruction-icon">🎯</div>
                            <p>Navigate the cube to the small hole to win</p>
                        </div>
                        <div class="instruction-item">
                            <div class="instruction-icon">🧩</div>
                            <p>Each level has unique geometry and challenges</p>
                        </div>
                        <div class="instruction-item">
                            <div class="instruction-icon">⌨️</div>
                            <p>Use arrow keys or WASD on desktop</p>
                        </div>
                    </div>
                    <button id="backFromHowToBtn" class="menu-btn">Back</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Social Share Modal -->
    <div id="shareModal" class="modal hidden">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>Share Your Score</h3>
            <p id="shareText">I just completed level X in Cube Move!</p>
            <div class="share-buttons">
                <button class="share-btn facebook" data-platform="facebook">Facebook</button>
                <button class="share-btn twitter" data-platform="twitter">Twitter</button>
                <button class="share-btn whatsapp" data-platform="whatsapp">WhatsApp</button>
                <button class="share-btn telegram" data-platform="telegram">Telegram</button>
                <button class="share-btn reddit" data-platform="reddit">Reddit</button>
                <button class="share-btn linkedin" data-platform="linkedin">LinkedIn</button>
            </div>
        </div>
    </div>

    <!-- Game Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/audio.js"></script>
    <script src="js/input.js"></script>
    <script src="js/cube.js"></script>
    <script src="js/level.js"></script>
    <script src="js/editor.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/game.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
