// UI management system for Cube Move game

class UIManager {
    constructor(game, audioManager) {
        this.game = game;
        this.audio = audioManager;
        this.isInitialized = false;
        
        // UI elements
        this.shareModal = null;
        this.currentScreen = 'menu';
        
        // Social sharing
        this.shareUrls = {
            facebook: 'https://www.facebook.com/sharer/sharer.php?u=',
            twitter: 'https://twitter.com/intent/tweet?text=',
            whatsapp: 'https://wa.me/?text=',
            telegram: 'https://t.me/share/url?url=',
            reddit: 'https://reddit.com/submit?url=',
            linkedin: 'https://www.linkedin.com/sharing/share-offsite/?url='
        };
    }

    init() {
        this.setupEventListeners();
        this.setupShareModal();
        this.isInitialized = true;
        console.log('UI Manager initialized');
    }

    setupEventListeners() {
        // Game overlay buttons
        this.setupOverlayButtons();
        
        // Share modal
        this.setupShareButtons();
        
        // Menu button click sounds
        this.addButtonSounds();
    }

    setupOverlayButtons() {
        const menuBtn = document.getElementById('menuBtn');
        if (menuBtn) {
            menuBtn.addEventListener('click', () => {
                this.hideGameOverlay();
                this.showMainMenu();
                this.audio.playMenuMusic();
            });
        }
    }

    setupShareModal() {
        this.shareModal = document.getElementById('shareModal');
        
        // Close button
        const closeBtn = this.shareModal?.querySelector('.close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideShareModal();
            });
        }
        
        // Click outside to close
        if (this.shareModal) {
            this.shareModal.addEventListener('click', (e) => {
                if (e.target === this.shareModal) {
                    this.hideShareModal();
                }
            });
        }
    }

    setupShareButtons() {
        const shareButtons = document.querySelectorAll('.share-btn');
        shareButtons.forEach(button => {
            button.addEventListener('click', () => {
                const platform = button.dataset.platform;
                this.shareScore(platform);
            });
        });
    }

    addButtonSounds() {
        // Add click sound to all buttons
        const buttons = document.querySelectorAll('button');
        buttons.forEach(button => {
            button.addEventListener('click', () => {
                this.audio.playButtonSound();
            });
        });
    }

    // Screen management
    showMainMenu() {
        this.hideAllScreens();
        this.currentScreen = 'menu';
        document.getElementById('mainMenu')?.classList.remove('hidden');
    }

    showLevelSelect() {
        this.hideAllScreens();
        this.currentScreen = 'levelSelect';
        document.getElementById('levelSelectScreen')?.classList.remove('hidden');
        this.updateLevelGrid();
    }

    showSettings() {
        this.hideAllScreens();
        this.currentScreen = 'settings';
        document.getElementById('settingsScreen')?.classList.remove('hidden');
    }

    showHowToPlay() {
        this.hideAllScreens();
        this.currentScreen = 'howToPlay';
        document.getElementById('howToPlayScreen')?.classList.remove('hidden');
    }

    hideAllScreens() {
        const screens = [
            'mainMenu', 'levelSelectScreen', 'settingsScreen', 
            'howToPlayScreen'
        ];
        screens.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.classList.add('hidden');
            }
        });
    }

    hideGameOverlay() {
        const overlay = document.getElementById('gameOverlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    // Level grid management
    updateLevelGrid() {
        const levelGrid = document.getElementById('levelGrid');
        if (!levelGrid) return;
        
        levelGrid.innerHTML = '';
        
        // Get game data
        const gameData = Utils.loadFromStorage('cubeMove_gameData', {
            unlockedLevels: 1,
            completedLevels: [],
            bestTimes: {}
        });
        
        for (let i = 1; i <= 30; i++) {
            const button = document.createElement('button');
            button.className = 'level-btn';
            button.textContent = i;
            
            if (i <= gameData.unlockedLevels) {
                if (gameData.completedLevels.includes(i)) {
                    button.classList.add('completed');
                    
                    // Add best time if available
                    if (gameData.bestTimes[i]) {
                        const time = gameData.bestTimes[i];
                        button.title = `Best time: ${time.toFixed(2)}s`;
                    }
                }
                
                button.addEventListener('click', () => {
                    this.startLevel(i);
                });
            } else {
                button.classList.add('locked');
                button.disabled = true;
                button.title = 'Complete previous levels to unlock';
            }

            levelGrid.appendChild(button);
        }
    }

    startLevel(levelNumber) {
        this.hideAllScreens();
        if (this.game) {
            this.game.startLevel(levelNumber);
        }
    }

    // Share functionality
    showShareModal(level, time) {
        if (!this.shareModal) return;
        
        const shareText = document.getElementById('shareText');
        if (shareText) {
            shareText.textContent = `I just completed level ${level} in Cube Move in ${time.toFixed(2)} seconds!`;
        }
        
        this.shareModal.classList.remove('hidden');
    }

    hideShareModal() {
        if (this.shareModal) {
            this.shareModal.classList.add('hidden');
        }
    }

    shareScore(platform) {
        const shareText = document.getElementById('shareText')?.textContent || 'Check out Cube Move!';
        const gameUrl = window.location.href;
        
        let shareUrl = '';
        
        switch (platform) {
            case 'facebook':
                shareUrl = this.shareUrls.facebook + encodeURIComponent(gameUrl);
                break;
            case 'twitter':
                shareUrl = this.shareUrls.twitter + encodeURIComponent(shareText + ' ' + gameUrl);
                break;
            case 'whatsapp':
                shareUrl = this.shareUrls.whatsapp + encodeURIComponent(shareText + ' ' + gameUrl);
                break;
            case 'telegram':
                shareUrl = this.shareUrls.telegram + encodeURIComponent(gameUrl) + '&text=' + encodeURIComponent(shareText);
                break;
            case 'reddit':
                shareUrl = this.shareUrls.reddit + encodeURIComponent(gameUrl) + '&title=' + encodeURIComponent(shareText);
                break;
            case 'linkedin':
                shareUrl = this.shareUrls.linkedin + encodeURIComponent(gameUrl);
                break;
        }
        
        if (shareUrl) {
            window.open(shareUrl, '_blank', 'width=600,height=400');
            this.hideShareModal();
        }
    }

    // Game state UI updates
    updateLevelDisplay(levelNumber) {
        const levelInfo = document.getElementById('currentLevel');
        if (levelInfo) {
            levelInfo.textContent = `Level ${levelNumber}`;
        }
    }

    updateMuteButton(isMuted) {
        const muteBtn = document.getElementById('muteBtn');
        if (muteBtn) {
            muteBtn.textContent = isMuted ? '🔇' : '🔊';
            muteBtn.title = isMuted ? 'Unmute' : 'Mute';
        }
    }

    updateFullscreenButton(isFullscreen) {
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        if (fullscreenBtn) {
            fullscreenBtn.textContent = isFullscreen ? '⛶' : '⛶';
            fullscreenBtn.title = isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen';
        }
    }

    // Notification system
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '15px 20px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: 'bold',
            zIndex: '10000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });
        
        // Set background color based on type
        switch (type) {
            case 'success':
                notification.style.backgroundColor = '#4ecdc4';
                break;
            case 'error':
                notification.style.backgroundColor = '#ff6b6b';
                break;
            case 'warning':
                notification.style.backgroundColor = '#ffa502';
                break;
            default:
                notification.style.backgroundColor = '#667eea';
        }
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Animate out and remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    }

    // Loading screen management
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
        }
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }
    }

    updateLoadingProgress(progress) {
        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = `${Math.min(100, Math.max(0, progress))}%`;
        }
    }

    // Game completion celebration
    showLevelCompleteAnimation() {
        // Create celebration particles
        this.createCelebrationParticles();
        
        // Show share button after a delay
        setTimeout(() => {
            const overlay = document.getElementById('gameOverlay');
            if (overlay && !overlay.classList.contains('hidden')) {
                const shareBtn = document.createElement('button');
                shareBtn.className = 'game-btn';
                shareBtn.textContent = 'Share Score';
                shareBtn.onclick = () => {
                    const level = this.game?.getCurrentLevel() || 1;
                    const time = this.game?.getGameTime() || 0;
                    this.showShareModal(level, time);
                };
                
                const buttonContainer = overlay.querySelector('.overlay-buttons');
                if (buttonContainer) {
                    buttonContainer.appendChild(shareBtn);
                }
            }
        }, 1000);
    }

    createCelebrationParticles() {
        const colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'];
        const particleCount = 50;
        
        for (let i = 0; i < particleCount; i++) {
            setTimeout(() => {
                this.createParticle(colors[i % colors.length]);
            }, i * 50);
        }
    }

    createParticle(color) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: fixed;
            width: 10px;
            height: 10px;
            background: ${color};
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            left: 50%;
            top: 50%;
        `;
        
        document.body.appendChild(particle);
        
        // Animate particle
        const angle = Math.random() * Math.PI * 2;
        const velocity = 100 + Math.random() * 200;
        const gravity = 300;
        const life = 2000 + Math.random() * 1000;
        
        let vx = Math.cos(angle) * velocity;
        let vy = Math.sin(angle) * velocity;
        let x = 0;
        let y = 0;
        
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const dt = 16 / 1000; // 60fps
            
            vy += gravity * dt;
            x += vx * dt;
            y += vy * dt;
            
            particle.style.transform = `translate(${x}px, ${y}px)`;
            particle.style.opacity = Math.max(0, 1 - elapsed / life);
            
            if (elapsed < life) {
                requestAnimationFrame(animate);
            } else {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }
        };
        
        requestAnimationFrame(animate);
    }

    // Responsive design helpers
    handleResize() {
        // Update UI elements for different screen sizes
        const gameContainer = document.getElementById('gameContainer');
        if (gameContainer) {
            const scale = Math.min(
                window.innerWidth / 768,
                window.innerHeight / 1024
            );
            
            // Adjust UI scaling if needed
            if (scale < 0.8) {
                document.body.classList.add('small-screen');
            } else {
                document.body.classList.remove('small-screen');
            }
        }
    }

    // Accessibility features
    setupAccessibility() {
        // Add keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                if (this.shareModal && !this.shareModal.classList.contains('hidden')) {
                    this.hideShareModal();
                } else if (this.currentScreen !== 'menu') {
                    this.showMainMenu();
                }
            }
        });
        
        // Add focus management
        const focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
        
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                const focusable = Array.from(document.querySelectorAll(focusableElements))
                    .filter(el => !el.disabled && el.offsetParent !== null);
                
                const currentIndex = focusable.indexOf(document.activeElement);
                
                if (e.shiftKey) {
                    // Shift + Tab (previous)
                    const prevIndex = currentIndex <= 0 ? focusable.length - 1 : currentIndex - 1;
                    focusable[prevIndex]?.focus();
                } else {
                    // Tab (next)
                    const nextIndex = currentIndex >= focusable.length - 1 ? 0 : currentIndex + 1;
                    focusable[nextIndex]?.focus();
                }
                
                e.preventDefault();
            }
        });
    }

    // Cleanup
    destroy() {
        // Remove event listeners and clean up
        this.isInitialized = false;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIManager;
}
