// Core game engine for Cube Move

class Game {
    constructor(stage, audioManager, inputManager) {
        this.stage = stage;
        this.audio = audioManager;
        this.input = inputManager;
        
        // Game state
        this.state = 'menu'; // menu, playing, paused, gameOver, levelComplete
        this.currentLevel = 1;
        this.isInitialized = false;
        
        // Game objects
        this.cube = null;
        this.level = null;
        this.gameContainer = new createjs.Container();
        this.uiContainer = new createjs.Container();
        
        // Timing
        this.lastTime = 0;
        this.deltaTime = 0;
        this.gameTime = 0;
        this.levelStartTime = 0;
        
        // Game settings
        this.cubeColor = '#FFD700';
        this.cubeSize = 1.0;
        
        // Level management
        this.levelManager = null;
        
        // Bind methods
        this.tick = this.tick.bind(this);
        this.handleInput = this.handleInput.bind(this);
    }

    async init() {
        try {
            // Add containers to stage
            this.stage.addChild(this.gameContainer);
            this.stage.addChild(this.uiContainer);
            
            // Initialize level manager
            this.levelManager = new LevelManager();
            await this.levelManager.init();
            
            // Setup input callbacks
            this.input.setSwipeCallback(this.handleInput);
            
            // Start game loop
            createjs.Ticker.framerate = 60;
            createjs.Ticker.addEventListener('tick', this.tick);
            
            this.isInitialized = true;
            console.log('Game engine initialized');
            
        } catch (error) {
            console.error('Failed to initialize game:', error);
            throw error;
        }
    }

    tick(event) {
        if (!this.isInitialized) return;
        
        // Calculate delta time
        const currentTime = Date.now();
        this.deltaTime = this.lastTime ? (currentTime - this.lastTime) / 1000 : 0;
        this.lastTime = currentTime;
        
        // Limit delta time to prevent large jumps
        this.deltaTime = Math.min(this.deltaTime, 1/30);
        
        // Update game time
        if (this.state === 'playing') {
            this.gameTime += this.deltaTime;
        }
        
        // Update game objects
        this.update(this.deltaTime);
        
        // Update stage
        this.stage.update();
    }

    update(deltaTime) {
        switch (this.state) {
            case 'playing':
                this.updatePlaying(deltaTime);
                break;
            case 'paused':
                // Don't update game objects when paused
                break;
            case 'levelComplete':
                this.updateLevelComplete(deltaTime);
                break;
            case 'gameOver':
                this.updateGameOver(deltaTime);
                break;
        }
    }

    updatePlaying(deltaTime) {
        // Update cube
        if (this.cube) {
            this.cube.update(deltaTime);
        }
        
        // Update level
        if (this.level) {
            this.level.update(deltaTime);
        }
        
        // Check win condition
        this.checkWinCondition();
        
        // Check lose condition
        this.checkLoseCondition();
    }

    updateLevelComplete(deltaTime) {
        // Update animations during level complete state
        if (this.cube) {
            this.cube.update(deltaTime);
        }
    }

    updateGameOver(deltaTime) {
        // Update animations during game over state
        if (this.cube) {
            this.cube.update(deltaTime);
        }
    }

    handleInput(direction, distance, duration) {
        if (this.state !== 'playing' || !this.cube || this.cube.isMoving) {
            return;
        }
        
        // Calculate movement distance based on level grid
        const moveDistance = this.level ? this.level.getGridSize() : 60;
        
        // Attempt to move cube
        const newPos = this.calculateNewPosition(direction, moveDistance);
        
        if (this.canMoveTo(newPos.x, newPos.y)) {
            this.moveCube(newPos.x, newPos.y, direction);
        } else {
            // Play fail sound and shake cube
            this.audio.playFailSound();
            if (this.cube) {
                this.cube.playShakeAnimation();
            }
        }
    }

    calculateNewPosition(direction, distance) {
        const currentPos = this.cube.getPosition();
        let newX = currentPos.x;
        let newY = currentPos.y;
        
        switch (direction) {
            case 'up':
                newY -= distance;
                break;
            case 'down':
                newY += distance;
                break;
            case 'left':
                newX -= distance;
                break;
            case 'right':
                newX += distance;
                break;
        }
        
        return { x: newX, y: newY };
    }

    canMoveTo(x, y) {
        if (!this.level) return false;
        
        // Check if position is within level bounds
        if (!this.level.isValidPosition(x, y)) {
            return false;
        }
        
        // Check for obstacles
        if (this.level.hasObstacle(x, y)) {
            return false;
        }
        
        return true;
    }

    moveCube(x, y, direction) {
        if (!this.cube) return;
        
        // Play move sound
        this.audio.playMoveSound();
        
        // Move cube with callback
        this.cube.moveTo(x, y, () => {
            this.onCubeMoveComplete(direction);
        });
    }

    onCubeMoveComplete(direction) {
        // Check for special tiles or events
        if (this.level) {
            this.level.onCubeMove(this.cube.getPosition(), direction);
        }
    }

    checkWinCondition() {
        if (!this.cube || !this.level) return;
        
        const cubePos = this.cube.getPosition();
        if (this.level.isWinPosition(cubePos.x, cubePos.y)) {
            this.levelComplete();
        }
    }

    checkLoseCondition() {
        if (!this.cube || !this.level) return;
        
        const cubePos = this.cube.getPosition();
        if (this.level.isLosePosition(cubePos.x, cubePos.y)) {
            this.gameOver();
        }
    }

    // Game state methods
    startLevel(levelNumber) {
        this.currentLevel = levelNumber;
        this.state = 'playing';
        this.gameTime = 0;
        this.levelStartTime = Date.now();
        
        // Clear previous level
        this.clearLevel();
        
        // Load new level
        this.loadLevel(levelNumber);
        
        // Update UI
        this.updateLevelUI();
        
        // Play game music
        this.audio.playGameMusic();
    }

    loadLevel(levelNumber) {
        // Get level data
        const levelData = this.levelManager.getLevel(levelNumber);
        if (!levelData) {
            console.error(`Level ${levelNumber} not found`);
            return;
        }
        
        // Create level
        this.level = new Level(levelData);
        this.gameContainer.addChild(this.level.container);
        
        // Create cube at starting position
        const startPos = this.level.getStartPosition();
        this.cube = new Cube(startPos.x, startPos.y, 40 * this.cubeSize, this.cubeColor);
        this.gameContainer.addChild(this.cube.container);
        
        console.log(`Level ${levelNumber} loaded`);
    }

    clearLevel() {
        // Remove cube
        if (this.cube) {
            this.cube.destroy();
            this.cube = null;
        }
        
        // Remove level
        if (this.level) {
            this.level.destroy();
            this.level = null;
        }
        
        // Clear game container
        this.gameContainer.removeAllChildren();
    }

    levelComplete() {
        if (this.state !== 'playing') return;
        
        this.state = 'levelComplete';
        
        // Play success sound
        this.audio.playLevelCompleteSound();
        
        // Calculate completion time
        const completionTime = (Date.now() - this.levelStartTime) / 1000;
        
        // Save progress
        this.saveProgress(this.currentLevel, completionTime);
        
        // Show level complete UI
        this.showLevelCompleteUI(completionTime);
        
        // Cube celebration animation
        if (this.cube) {
            this.cube.playBounceAnimation();
        }
        
        console.log(`Level ${this.currentLevel} completed in ${completionTime.toFixed(2)}s`);
    }

    gameOver() {
        if (this.state !== 'playing') return;
        
        this.state = 'gameOver';
        
        // Play game over sound
        this.audio.playGameOverSound();
        
        // Show game over UI
        this.showGameOverUI();
        
        // Cube failure animation
        if (this.cube) {
            this.cube.playShakeAnimation();
        }
        
        console.log(`Game over on level ${this.currentLevel}`);
    }

    restartLevel() {
        this.startLevel(this.currentLevel);
    }

    nextLevel() {
        const nextLevelNumber = this.currentLevel + 1;
        if (this.levelManager.hasLevel(nextLevelNumber)) {
            this.startLevel(nextLevelNumber);
        } else {
            // Game completed
            this.gameCompleted();
        }
    }

    gameCompleted() {
        this.state = 'gameComplete';
        this.audio.playVictoryMusic();
        // TODO: Show game completion screen
        console.log('Game completed!');
    }

    pause() {
        if (this.state === 'playing') {
            this.state = 'paused';
            this.audio.pauseMusic();
            this.showPauseUI();
        }
    }

    resume() {
        if (this.state === 'paused') {
            this.state = 'playing';
            this.audio.resumeMusic();
            this.hidePauseUI();
        }
    }

    togglePause() {
        if (this.state === 'playing') {
            this.pause();
        } else if (this.state === 'paused') {
            this.resume();
        }
    }

    // UI methods (these will interact with the HTML UI)
    updateLevelUI() {
        const levelInfo = document.getElementById('currentLevel');
        if (levelInfo) {
            levelInfo.textContent = `Level ${this.currentLevel}`;
        }
    }

    showLevelCompleteUI(completionTime) {
        const overlay = document.getElementById('gameOverlay');
        const title = document.getElementById('overlayTitle');
        const message = document.getElementById('overlayMessage');
        const nextBtn = document.getElementById('nextLevelBtn');
        const retryBtn = document.getElementById('retryBtn');

        if (overlay && title && message) {
            title.textContent = 'Level Complete!';
            message.textContent = `Completed in ${completionTime.toFixed(2)} seconds`;
            overlay.classList.remove('hidden');

            // Setup button handlers
            if (nextBtn) {
                nextBtn.style.display = 'block';
                nextBtn.onclick = () => {
                    overlay.classList.add('hidden');
                    this.nextLevel();
                };
            }

            if (retryBtn) {
                retryBtn.onclick = () => {
                    overlay.classList.add('hidden');
                    this.restartLevel();
                };
            }
        }
    }

    showGameOverUI() {
        const overlay = document.getElementById('gameOverlay');
        const title = document.getElementById('overlayTitle');
        const message = document.getElementById('overlayMessage');
        const nextBtn = document.getElementById('nextLevelBtn');
        const retryBtn = document.getElementById('retryBtn');

        if (overlay && title && message) {
            title.textContent = 'Game Over';
            message.textContent = 'Try again!';
            overlay.classList.remove('hidden');

            // Hide next level button
            if (nextBtn) {
                nextBtn.style.display = 'none';
            }

            // Setup retry button
            if (retryBtn) {
                retryBtn.onclick = () => {
                    overlay.classList.add('hidden');
                    this.restartLevel();
                };
            }
        }
    }

    showPauseUI() {
        // TODO: Implement pause UI
        console.log('Game paused');
    }

    hidePauseUI() {
        // TODO: Implement pause UI
        console.log('Game resumed');
    }

    // Settings methods
    updateCubeColor(color) {
        this.cubeColor = color;
        if (this.cube) {
            this.cube.setColor(color);
        }
    }

    updateCubeSize(size) {
        this.cubeSize = size;
        if (this.cube) {
            this.cube.setSize(size);
        }
    }

    // Progress saving
    saveProgress(levelNumber, completionTime) {
        // This would typically save to localStorage or server
        const gameData = Utils.loadFromStorage('cubeMove_gameData', {
            unlockedLevels: 1,
            completedLevels: [],
            bestTimes: {}
        });

        // Unlock next level
        gameData.unlockedLevels = Math.max(gameData.unlockedLevels, levelNumber + 1);

        // Mark level as completed
        if (!gameData.completedLevels.includes(levelNumber)) {
            gameData.completedLevels.push(levelNumber);
        }

        // Save best time
        if (!gameData.bestTimes[levelNumber] || completionTime < gameData.bestTimes[levelNumber]) {
            gameData.bestTimes[levelNumber] = completionTime;
        }

        Utils.saveToStorage('cubeMove_gameData', gameData);
    }

    // State queries
    isPlaying() {
        return this.state === 'playing';
    }

    isPaused() {
        return this.state === 'paused';
    }

    getCurrentLevel() {
        return this.currentLevel;
    }

    getGameTime() {
        return this.gameTime;
    }

    // Cleanup
    destroy() {
        createjs.Ticker.removeEventListener('tick', this.tick);

        this.clearLevel();

        if (this.stage) {
            this.stage.removeChild(this.gameContainer);
            this.stage.removeChild(this.uiContainer);
        }

        this.input = null;
        this.audio = null;
        this.stage = null;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Game;
}
