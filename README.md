# Cube Move - HTML5 Puzzle Game

A complete HTML5 puzzle game where players control a yellow cube to navigate through various geometries and reach the goal. Built with CreateJS framework and featuring 30 levels, a level editor, and comprehensive customization options.

## Features

### Core Gameplay
- **30 Challenging Levels**: Progressive difficulty with unique geometries
- **Smooth Controls**: Touch, mouse, and keyboard support
- **Multiple Input Methods**: Swipe gestures, WASD keys, arrow keys
- **Responsive Design**: Auto-scaling from 768×1024 base resolution
- **Mobile Optimized**: Portrait mode with rotation instructions

### Game Systems
- **Level Editor**: Built-in editor for creating custom levels
- **Progress Tracking**: Save game progress and best times
- **Settings & Customization**: Cube colors, sizes, and game preferences
- **Audio System**: Sound effects and background music with volume controls
- **Social Sharing**: Share scores on Facebook, Twitter, WhatsApp, and more

### Technical Features
- **CreateJS Framework**: Professional 2D graphics and animations
- **Web Audio API**: High-quality audio with fade effects
- **Local Storage**: Persistent game data and settings
- **Fullscreen Support**: Immersive gaming experience
- **Browser Compatibility**: Modern browser support with fallbacks

## Game Controls

### Desktop
- **Arrow Keys** or **WASD**: Move the cube
- **M**: Toggle mute
- **F**: Toggle fullscreen
- **R**: Restart level
- **Escape**: Pause game

### Mobile/Touch
- **Swipe**: Move the cube in swipe direction
- **Tap**: UI interactions
- **Pinch**: Zoom (where applicable)

## File Structure

```
cube-move/
├── index.html              # Main HTML file
├── css/
│   └── style.css          # Complete styling and responsive design
├── js/
│   ├── main.js            # Main application and initialization
│   ├── game.js            # Core game engine and logic
│   ├── cube.js            # Cube class with animations
│   ├── level.js           # Level system and manager
│   ├── input.js           # Input handling (touch/mouse/keyboard)
│   ├── audio.js           # Audio management system
│   ├── ui.js              # UI management and social sharing
│   ├── editor.js          # Level editor functionality
│   └── utils.js           # Utility functions and helpers
├── assets/
│   └── audio/             # Audio files directory
└── README.md              # This file
```

## Getting Started

1. **Clone or Download**: Get the game files
2. **Open in Browser**: Open `index.html` in a modern web browser
3. **Play**: Use the main menu to start playing or create levels

### Local Development
For local development with audio support, serve the files through a web server:

```bash
# Using Python 3
python -m http.server 8000

# Using Node.js (with http-server)
npx http-server

# Using PHP
php -S localhost:8000
```

Then open `http://localhost:8000` in your browser.

## Game Mechanics

### Objective
Navigate the yellow cube from the starting position to the goal hole by moving through the available path tiles.

### Level Elements
- **Path Tiles**: Safe areas where the cube can move
- **Obstacles**: Walls, spikes, and holes that block or harm the cube
- **Goal**: The target hole where the cube must reach
- **Special Tiles**: Teleporters, switches, and ice tiles (advanced levels)

### Scoring
- **Completion Time**: Faster completion = better score
- **Level Progress**: Unlock new levels by completing previous ones
- **Best Times**: Track your personal best for each level

## Level Editor

The built-in level editor allows you to create custom levels:

### Tools
- **Path**: Create walkable tiles
- **Obstacle**: Add walls, spikes, or holes
- **Start**: Set cube starting position
- **Goal**: Set the target position
- **Special**: Add special tiles (teleporters, switches, ice)
- **Erase**: Remove tiles

### Features
- **Grid-based Design**: Easy tile placement
- **Visual Preview**: See your level as you build
- **Save/Load**: Store custom levels locally
- **Test Mode**: Play your levels before saving

## Customization Options

### Cube Settings
- **Color**: Choose from multiple cube colors
- **Size**: Adjust cube size (0.5x to 2.0x)
- **Trail Effects**: Enable/disable particle trails

### Game Settings
- **Audio**: Separate controls for music and sound effects
- **Controls**: Adjust swipe sensitivity
- **Graphics**: Quality and performance options

## Browser Support

### Minimum Requirements
- **Chrome**: 60+
- **Firefox**: 55+
- **Safari**: 12+
- **Edge**: 79+

### Required Features
- HTML5 Canvas
- Web Audio API (optional, graceful fallback)
- Local Storage
- Touch Events (mobile)
- CreateJS compatibility

## Performance

### Optimizations
- **60 FPS Target**: Smooth animations and gameplay
- **Memory Management**: Efficient object pooling
- **Asset Loading**: Progressive loading with fallbacks
- **Mobile Performance**: Optimized for touch devices

### System Requirements
- **RAM**: 512MB available
- **CPU**: Modern dual-core processor
- **GPU**: Hardware acceleration recommended

## Development

### Architecture
- **Modular Design**: Separate classes for each system
- **Event-Driven**: Loose coupling between components
- **Responsive**: Mobile-first design approach
- **Extensible**: Easy to add new features and levels

### Key Classes
- **GameApp**: Main application controller
- **Game**: Core game logic and state management
- **Level**: Level data and rendering
- **Cube**: Player character with animations
- **InputManager**: Cross-platform input handling
- **AudioManager**: Sound and music system
- **UIManager**: Interface and social features

## Contributing

To add new features or levels:

1. **Levels**: Use the built-in level editor or modify `js/level.js`
2. **Features**: Extend the appropriate class files
3. **Styling**: Update `css/style.css` for visual changes
4. **Audio**: Add files to `assets/audio/` directory

## License

This project is open source. Feel free to use, modify, and distribute according to your needs.

## Credits

- **Framework**: CreateJS for 2D graphics and animations
- **Design**: Modern responsive web design principles
- **Audio**: Web Audio API with graceful fallbacks
- **Icons**: Custom CSS-based icons and animations

## Support

For issues or questions:
1. Check browser console for error messages
2. Ensure modern browser with required features
3. Verify local server setup for audio support
4. Clear browser cache if experiencing issues

---

**Enjoy playing Cube Move!** 🎮✨
